# Variáveis para Templates de Documentos

Este documento lista todas as variáveis disponíveis para uso nos templates de documentos (propostas, contratos, etc.) do sistema Ageu.

## Como usar as variáveis

Nos documentos DOCX, as variáveis devem ser inseridas usando a sintaxe de chaves duplas: `{{nome_variavel}}`.

Por exemplo, para inserir o nome do cliente, use: `{{nome_cliente}}`

## Variáveis Disponíveis

### Informações da Proposta

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `{{proposta}}` | Nome da proposta | Reforma do Edifício XYZ |
| `{{nome_proposta}}` | Nome da proposta (alternativo) | Reforma do Edifício XYZ |
| `{{orcamento}}` | Valor total da proposta | R$ 100.000,00 |
| `{{valor_proposta}}` | Valor total da proposta (alternativo) | R$ 100.000,00 |
| `{{valor_proposta_extenso}}` | Valor total da proposta por extenso | cem mil reais |
| `{{valor_80_porcento}}` | 80% do valor da proposta | R$ 80.000,00 |
| `{{valor_80_porcento_extenso}}` | 80% do valor da proposta por extenso | oitenta mil reais |

### Informações de Pagamento

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `{{entrada}}` | Valor da entrada | R$ 20.000,00 |
| `{{valor_entrada}}` | Valor da entrada (alternativo) | R$ 20.000,00 |
| `{{valor_entrada_extenso}}` | Valor da entrada por extenso | vinte mil reais |
| `{{num_parcelas}}` | Número de parcelas | 10 |
| `{{quantidade_parcelas}}` | Número de parcelas (alternativo) | 10 |
| `{{valor_parcela}}` | Valor de cada parcela | R$ 8.000,00 |
| `{{valor_parcela_extenso}}` | Valor de cada parcela por extenso | oito mil reais |

### Informações de Prazo

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `{{prazo_dias}}` | Quantidade de dias entre início e fim | 90 |
| `{{data_inicio}}` | Data de início do projeto | 01/01/2024 |
| `{{data_fim}}` | Data de término do projeto | 01/04/2024 |

### Informações do Cliente

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `{{nome_cliente}}` | Nome do cliente | Empresa ABC Ltda |
| `{{nome_cliente_maiusculo}}` | Nome do cliente em maiúsculas | EMPRESA ABC LTDA |
| `{{cpf_cnpj}}` | CPF ou CNPJ do cliente | 12.345.678/0001-90 |
| `{{endereco}}` | Endereço completo do cliente | Rua ABC, 123, CEP: 12345-678, São Paulo/SP |
| `{{endereco_completo}}` | Endereço completo do cliente (alternativo) | Rua ABC, 123, CEP: 12345-678, São Paulo/SP |
| `{{endereco_maiusculo}}` | Endereço completo do cliente em maiúsculas | RUA ABC, 123, CEP: 12345-678, SÃO PAULO/SP |

### Outras Informações

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `{{data_hoje}}` | Data atual | 15/05/2024 |
| `{{data_hoje_extenso}}` | Data atual por extenso | 15 de maio de 2024 |

## Como Usar o Popup de Variáveis

Ao editar um documento no editor, você pode usar o botão "Variáveis" no canto superior direito para abrir um popup com todas as variáveis disponíveis. Para usar:

1. Clique no botão "Variáveis"
2. Clique na variável desejada no popup para copiá-la para a área de transferência
3. Posicione o cursor no documento onde deseja inserir a variável
4. Cole a variável usando Ctrl+V (ou Command+V no Mac)

O popup pode ser arrastado para qualquer posição na tela usando a barra superior. Quando uma variável é copiada, você verá um ícone de marcação verde e uma notificação confirmando a cópia.

## Adicionando Novas Variáveis

Para adicionar novas variáveis ao sistema, é necessário:

1. Atualizar a função `generateProposalTemplateVariables` no arquivo `src/actions/proposals.ts`
2. Atualizar o componente `TemplateVariablesPopup` no arquivo `src/components/template-variables-popup.tsx`
3. Atualizar esta documentação

Exemplo de como adicionar uma nova variável:

```typescript
export async function generateProposalTemplateVariables(proposal: any) {
  // ... código existente ...

  return {
    // ... variáveis existentes ...

    // Nova variável
    nova_variavel: "Valor da nova variável",
  };
}
```
