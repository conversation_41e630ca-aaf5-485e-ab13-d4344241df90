generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String       @id @default(uuid())
  email      String       @unique
  name       String
  document   String?
  phone      String?
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  googleId   String?
  image      String?
  Membership Membership[]
}

model Membership {
  id             String       @id @default(cuid())
  role           Role         @default(MEMBER)
  enabled        Boolean      @default(false)
  userId         String
  organizationId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, organizationId])
  @@index([organizationId])
  @@index([userId])
}

model Organization {
  id                String             @id @default(cuid())
  name              String
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  customers         Customer[]
  labors            Labor[]
  memberships       Membership[]
  proposalTemplates ProposalTemplate[]
  reportTemplates   ReportTemplate[]
  serviceScopes     ServiceScope[]
}

model Customer {
  id             String        @id @default(uuid())
  name           String
  email          String        @unique
  document       String
  phone          String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  observation    String?
  documentType   TypeDocument?
  organizationId String
  contacts       Contact[]
  organization   Organization  @relation(fields: [organizationId], references: [id])
  projects       Project[]
  proposals      Proposal[]

  @@index([email])
}

model Contact {
  id         String      @id @default(uuid())
  name       String
  type       ContactType
  customerId String
  date       DateTime
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  customer   Customer    @relation(fields: [customerId], references: [id])

  @@index([customerId])
}

model Project {
  id            String        @id @default(uuid())
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  customerId    String
  area          Decimal?      @default(0)
  contractValue Decimal?      @default(0)
  endDate       DateTime?
  project       String?
  startDate     DateTime?
  serviceType   String?
  customer      Customer      @relation(fields: [customerId], references: [id])
  projectFiles  ProjectFile[] @relation("ProjectToProjectFile")
  files         File[]        @relation("ProjectFiles")

  @@index([customerId])
}

model ProjectFile {
  projectId String
  fileId    String
  file      File    @relation("ProjectFiles", fields: [fileId], references: [id], onDelete: Cascade)
  project   Project @relation("ProjectToProjectFile", fields: [projectId], references: [id], onDelete: Cascade)

  @@id([projectId, fileId])
  @@index([fileId])
  @@index([projectId])
}

model File {
  id            String        @id @default(cuid())
  path          String
  name          String
  type          String
  uploadedAt    DateTime      @default(now())
  size          Int
  thumbnailPath String?
  Contract      Contract[]
  photos        Photo[]
  projectFiles  ProjectFile[] @relation("ProjectFiles")
  proposals     Proposal[]
  projects      Project[]     @relation("ProjectFiles")
}

model Proposal {
  id                   String                  @id @default(uuid())
  name                 String
  startDate            DateTime
  endDate              DateTime
  periodicity          Periodicity
  createdAt            DateTime                @default(now())
  customService        String?
  customerId           String
  situation            ProposalSituation       @default(NEW)
  updatedAt            DateTime                @updatedAt
  budget               Decimal
  order                Int                     @default(0)
  proposalTemplateId   String?
  fileId               String?
  area                 Decimal
  downPayment          Decimal?
  installmentAmount    Decimal?
  installmentNumber    Int?
  paymentCondition     PaymentCondition
  address              String?
  cep                  String?
  city                 String?
  state                String?
  methodology          String[]
  serviceType          String?
  fileEditorId         String?
  workTotalCost        Decimal?
  contract             Contract?
  inspectionParameters InspectionParameter[]
  logs                 LogProposal[]
  plannings            PlanningFrequencyItem[]
  Productivity         Productivity[]
  customer             Customer                @relation(fields: [customerId], references: [id])
  fileEditor           FileEditor?             @relation(fields: [fileEditorId], references: [id])
  file                 File?                   @relation(fields: [fileId], references: [id])
  proposalTemplate     ProposalTemplate?       @relation(fields: [proposalTemplateId], references: [id])
  repairBudgets        RepairBudget[]
  serviceScopes        ServiceScope[]
}

model Contract {
  id           String      @id @default(uuid())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  proposalId   String      @unique
  fileId       String?
  fileEditorId String?
  fileEditor   FileEditor? @relation(fields: [fileEditorId], references: [id])
  File         File?       @relation(fields: [fileId], references: [id])
  proposal     Proposal    @relation(fields: [proposalId], references: [id])
}

model PlanningFrequencyItem {
  id             String         @id @default(uuid())
  order          Int            @default(0)
  content        String
  proposalId     String
  label          String
  repairBudgetId String?
  proposal       Proposal       @relation(fields: [proposalId], references: [id])
  Productivity   Productivity[]
  repairBudgets  RepairBudget[] @relation("PlanningFrequencyItemsToRepairBudgets")
}

model ServiceScope {
  id             String         @id @default(uuid())
  name           String
  description    String
  createdAt      DateTime       @default(now())
  proposalId     String?
  updatedAt      DateTime       @updatedAt
  types          ServiceType[]
  organizationId String
  productivity   Productivity[]
  repairBudgets  RepairBudget[]
  organization   Organization   @relation(fields: [organizationId], references: [id])
  proposal       Proposal?      @relation(fields: [proposalId], references: [id])

  @@index([proposalId])
}

model ProposalTemplate {
  id             String               @id @default(uuid())
  title          String
  description    String
  createdAt      DateTime             @default(now())
  type           typeProposalContract
  fileEditorId   String
  organizationId String
  proposals      Proposal[]
  fileEditor     FileEditor           @relation(fields: [fileEditorId], references: [id])
  organization   Organization         @relation(fields: [organizationId], references: [id])
}

model Productivity {
  id                        String                @id @default(uuid())
  order                     Int                   @default(0)
  label                     String?
  periodicityId             String
  serviceId                 String
  startDate                 DateTime?
  endDate                   DateTime?
  buildingPercentage        Decimal               @default(0)
  predictedPeriodPercentage Decimal?
  realPeriodPercentage      Decimal?
  description               String?
  workersQuantity           Int
  toolsQuantity             Int?
  toolsDescription          String?
  repairBudgetId            String
  proposalId                String
  colorChart                String?
  colorChartPredicted       String?
  periodicity               PlanningFrequencyItem @relation(fields: [periodicityId], references: [id])
  proposal                  Proposal              @relation(fields: [proposalId], references: [id])
  repairBudget              RepairBudget          @relation(fields: [repairBudgetId], references: [id])
  service                   ServiceScope          @relation(fields: [serviceId], references: [id])

  @@unique([proposalId, repairBudgetId, periodicityId])
  @@index([proposalId, repairBudgetId])
  @@index([periodicityId])
}

model Pluviosity {
  id      String  @id @default(uuid())
  value   Decimal
  city    String
  dateKey String
  logs    String?
  saved   Boolean @default(false)

  @@unique([dateKey, city])
}

model InspectionParameter {
  id               String                 @id @default(uuid())
  technicalData    String
  inspectionDate   DateTime
  observation      String
  proposalId       String
  numberInspection Int?
  proposal         Proposal               @relation(fields: [proposalId], references: [id])
  laborEquipament  LaborEquipmentAmount[] @relation("LaborEquipamentToInspectionParameter")
  photos           Photo[]
}

model Photo {
  id                    String               @id @default(uuid())
  description           String
  fileId                String?
  inspectionParameterId String?
  order                 Int?
  file                  File?                @relation(fields: [fileId], references: [id])
  inspectionParameter   InspectionParameter? @relation(fields: [inspectionParameterId], references: [id])
}

model RepairBudget {
  id                     String                  @id @default(uuid())
  measurementDate        DateTime
  equipmentDescription   String?
  laborAmount            Int?
  equipmentAmount        Int?
  gravity                Int                     @default(0)
  urgency                Int                     @default(0)
  tendency               Int                     @default(0)
  gut                    Int                     @default(0)
  serviceCost            Decimal                 @default(0)
  totalCost              Decimal                 @default(0)
  financialWeight        Decimal                 @default(0)
  igrf                   Decimal                 @default(0)
  imp                    Decimal                 @default(0)
  buildingPercentage     Decimal                 @default(0)
  description            String?
  createdAt              DateTime                @default(now())
  proposalId             String
  serviceScopeId         String
  endDate                DateTime?
  startDate              DateTime?
  laborEquipament        LaborEquipmentAmount[]  @relation("LaborEquipamentToRepairBudget")
  productivity           Productivity[]
  proposal               Proposal                @relation(fields: [proposalId], references: [id])
  serviceScope           ServiceScope            @relation(fields: [serviceScopeId], references: [id])
  planningFrequencyItems PlanningFrequencyItem[] @relation("PlanningFrequencyItemsToRepairBudgets")
}

model ReportTemplate {
  id             String       @id @default(uuid())
  title          String
  description    String
  createdAt      DateTime     @default(now())
  type           ReportType
  fileEditorId   String
  organizationId String
  fileEditor     FileEditor   @relation(fields: [fileEditorId], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id])
}

model FileEditor {
  id               String             @id @default(uuid())
  filename         String
  mimetype         String
  size             Int
  key              String             @unique
  bucket           String
  isLocked         Boolean            @default(false)
  lockedBy         String[]
  version          BigInt?
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  Contract         Contract[]
  Proposal         Proposal[]
  ProposalTemplate ProposalTemplate[]
  ReportTemplate   ReportTemplate[]
}

model Labor {
  id                   String                 @id @default(uuid())
  name                 String
  description          String?
  type                 LaborEquipmentType
  laborType            LaborType?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  organizationId       String
  organization         Organization           @relation(fields: [organizationId], references: [id])
  LaborEquipmentAmount LaborEquipmentAmount[]
}

model LaborEquipmentAmount {
  id                    String               @id @default(uuid())
  amount                Int?
  laborId               String
  repairBudgetId        String?
  inspectionParameterId String?
  InspectionParameter   InspectionParameter? @relation("LaborEquipamentToInspectionParameter", fields: [inspectionParameterId], references: [id])
  labor                 Labor                @relation(fields: [laborId], references: [id])
  RepairBudget          RepairBudget?        @relation("LaborEquipamentToRepairBudget", fields: [repairBudgetId], references: [id])
}

model LogProposal {
  id         String            @id @default(uuid())
  proposalId String
  oldStatus  ProposalSituation
  newStatus  ProposalSituation
  createdAt  DateTime          @default(now())
  proposal   Proposal          @relation(fields: [proposalId], references: [id])

  @@index([proposalId])
}

enum Periodicity {
  WEEKLY
  MONTHLY
  NONE
}

enum ContactType {
  EMAIL
  PHONE
}

enum ProposalSituation {
  NEW
  UNDER_ANALYSIS
  PROPOSAL_SENT
  PROPOSAL_ACCEPTED
  SIGN_REQUESTED
  SIGNED
  PROJECT_IN_PROGRESS
  PROJECT_FINISHED
  LOST
}

enum PaymentCondition {
  CASH
  INSTALLMENTS
}

enum ServiceType {
  PROPOSAL_SERVICE
  REPAIR_SERVICE
}

enum LaborType {
  DIRECT
  INDIRECT
  OUTSOURCED
}

enum LaborEquipmentType {
  EQUIPAMENT
  LABOR
}

enum ReportType {
  REPORT
  KPI
  HISTOGRAM
  CLIMATE_IMPACT
  PROJECT
  CONSULTANCY
}

enum Role {
  OWNER
  MEMBER
}

enum typeProposalContract {
  CONTRACT
  PROPOSAL
  INSPECTION
  SUPERVISION
  PROJECT
  CONSULTANCY
}

enum TypeDocument {
  CPF
  CNPJ
  RG
  CNH
}
