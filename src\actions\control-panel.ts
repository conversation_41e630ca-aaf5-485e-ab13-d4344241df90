"use server";

import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import { ControlPanelData } from "@/src/types/core/control-panel";
import { auth } from "@/src/providers/auth";

/**
 * Calcula a taxa de conversão do mês anterior
 * @param organizationId ID da organização
 * @returns Objeto com total de propostas e propostas aceitas do mês anterior
 */
async function calculatePreviousMonthConversionRate(organizationId: string) {
  try {
    // Obter o primeiro dia do mês atual e do mês anterior
    const today = new Date();
    const startOfCurrentMonth = new Date(
      today.getFullYear(),
      today.getMonth(),
      1
    );
    const startOfPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    );

    // Filtro base para o mês anterior
    const baseFilter = {
      customer: {
        organizationId,
      },
      createdAt: {
        lt: startOfCurrentMonth,
        gte: startOfPreviousMonth,
      },
    };

    // Contar total de propostas do mês anterior
    const totalProposals = await prisma.proposal.count({
      where: baseFilter,
    });

    // Contar propostas aceitas, assinadas, em andamento e concluídas do mês anterior
    const acceptedProposals = await prisma.proposal.count({
      where: {
        ...baseFilter,
        situation: {
          in: [
            "PROPOSAL_ACCEPTED",
            "SIGNED",
            "PROJECT_IN_PROGRESS",
            "PROJECT_FINISHED",
          ],
        },
      },
    });

    return {
      totalProposals,
      acceptedProposals,
    };
  } catch (error) {
    console.error("Erro ao calcular taxa de conversão do mês anterior:", error);
    return {
      totalProposals: 0,
      acceptedProposals: 0,
    };
  }
}

/**
 * Calcula a tendência (% de crescimento ou queda) comparando o valor atual com o valor do mês anterior
 * @param model Nome do modelo no Prisma (customer, proposal, etc.)
 * @param filter Filtros adicionais para a consulta
 * @param currentCount Contagem atual dos registros
 * @returns Percentual de crescimento/queda
 */
async function calculateTrend(
  model: string,
  filter: any = {},
  currentCount: number
): Promise<number> {
  try {
    // Obter o primeiro dia do mês atual e do mês anterior
    const today = new Date();
    const startOfCurrentMonth = new Date(
      today.getFullYear(),
      today.getMonth(),
      1
    );
    const startOfPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1
    );

    // Adicionar filtro de organização
    const session = await auth();
    const organizationId = session?.membership?.organizationId;

    if (!organizationId) {
      return 0;
    }

    // Construir o filtro completo
    const completeFilter: any = {
      ...filter,
      createdAt: {
        lt: startOfCurrentMonth,
        gte: startOfPreviousMonth,
      },
    };

    // Adicionar filtro de organização dependendo do modelo
    if (model === "customer") {
      completeFilter.organizationId = organizationId;
    } else if (model === "proposal") {
      completeFilter.customer = {
        organizationId,
      };
    }

    // Contar registros do mês anterior
    const previousCount = await prisma[model].count({
      where: completeFilter,
    });

    // Calcular a tendência
    if (previousCount > 0) {
      return Number(
        (((currentCount - previousCount) / previousCount) * 100).toFixed(1)
      );
    }

    return 0;
  } catch (error) {
    console.error("Erro ao calcular tendência:", error);
    return 0;
  }
}

export async function loadControlPanelData() {
  try {
    const session = await auth();
    const organizationId = session?.membership?.organizationId;

    if (!organizationId) {
      throw new Error("No organization found for current user");
    }

    const customerCount = await prisma.customer.count({
      where: { organizationId },
    });

    const totalProposalsCount = await prisma.proposal.count({
      where: {
        customer: {
          organizationId,
        },
      },
    });

    const lostProposalsCount = await prisma.proposal.count({
      where: {
        situation: "LOST",
        customer: {
          organizationId,
        },
      },
    });

    const underAnalysisProposalsCount = await prisma.proposal.count({
      where: {
        situation: "UNDER_ANALYSIS",
        customer: {
          organizationId,
        },
      },
    });

    const acceptedProposalsCount = await prisma.proposal.count({
      where: {
        situation: "PROPOSAL_ACCEPTED",
        customer: {
          organizationId,
        },
      },
    });

    const signedProposalsCount = await prisma.proposal.count({
      where: {
        situation: "SIGNED",
        customer: {
          organizationId,
        },
      },
    });

    const inProgressProjectsCount = await prisma.proposal.count({
      where: {
        situation: "PROJECT_IN_PROGRESS",
        customer: {
          organizationId,
        },
      },
    });

    const completedProjectsCount = await prisma.proposal.count({
      where: {
        situation: "PROJECT_FINISHED",
        customer: {
          organizationId,
        },
      },
    });

    // Buscar dados de propostas por tipo de contrato (serviceType)
    // Definir os tipos de contrato que queremos contar
    const contractTypes = [
      { id: "INSPECAO", name: "Inspeção" },
      { id: "FISCALIZACAO", name: "Fiscalização" },
      { id: "GERENCIAMENTO", name: "Gerenciamento" },
      { id: "CONSULTORIA", name: "Consultoria" },
      { id: "PROJETO", name: "Projeto" },
    ];

    // Contar propostas por tipo de contrato
    const serviceTypeCounts = await Promise.all(
      contractTypes.map(async (type) => {
        const count = await prisma.proposal.count({
          where: {
            serviceType: type.id,
            customer: {
              organizationId,
            },
          },
        });
        return {
          name: type.name,
          count,
        };
      })
    );

    // Ordenar por contagem (do maior para o menor)
    const topServices = serviceTypeCounts.sort((a, b) => b.count - a.count);

    // Calcular a tendência para clientes ativos usando a função reutilizável
    const customerTrend = await calculateTrend("customer", {}, customerCount);

    // Calcular a taxa de conversão atual
    const conversionRate =
      totalProposalsCount > 0
        ? Number(
            (
              ((acceptedProposalsCount +
                signedProposalsCount +
                inProgressProjectsCount +
                completedProjectsCount) /
                totalProposalsCount) *
              100
            ).toFixed(1)
          )
        : 0;

    // Calcular a taxa de conversão do mês anterior
    const previousMonthData = await calculatePreviousMonthConversionRate(
      organizationId
    );
    const previousConversionRate =
      previousMonthData.totalProposals > 0
        ? Number(
            (
              (previousMonthData.acceptedProposals /
                previousMonthData.totalProposals) *
              100
            ).toFixed(1)
          )
        : 0;

    // Calcular a tendência da taxa de conversão
    const conversionRateTrend =
      previousConversionRate > 0
        ? Number(
            (
              ((conversionRate - previousConversionRate) /
                previousConversionRate) *
              100
            ).toFixed(1)
          )
        : 0;

    const data = [
      {
        title: "Clientes ativos",
        count: customerCount,
        trend: customerTrend,
      },
      {
        title: "Total de propostas",
        count: totalProposalsCount,
        trend: await calculateTrend("proposal", {}, totalProposalsCount),
      },
      {
        title: "Taxa de conversão",
        count: `${conversionRate}%`,
        trend: conversionRateTrend,
      },
      {
        title: "Propostas perdidas",
        count: lostProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "LOST" },
          lostProposalsCount
        ),
      },
      {
        title: "Propostas em analise",
        count: underAnalysisProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "UNDER_ANALYSIS" },
          underAnalysisProposalsCount
        ),
      },
      {
        title: "Propostas aceitas",
        count: acceptedProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROPOSAL_ACCEPTED" },
          acceptedProposalsCount
        ),
      },
      {
        title: "Propostas assinadas",
        count: signedProposalsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "SIGNED" },
          signedProposalsCount
        ),
      },
      {
        title: "Projetos em andamento",
        count: inProgressProjectsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROJECT_IN_PROGRESS" },
          inProgressProjectsCount
        ),
      },
      {
        title: "Projetos concluidos",
        count: completedProjectsCount,
        trend: await calculateTrend(
          "proposal",
          { situation: "PROJECT_FINISHED" },
          completedProjectsCount
        ),
      },
      // Dados dos tipos de contrato
      ...topServices.map((service) => ({
        title: service.name,
        count: service.count,
        type: "CONTRACT_TYPE", // Adicionar um identificador para diferenciar estes dados
      })),
    ];

    return parseObject(data) as ControlPanelData[];
  } catch (error) {
    console.error(error);
  }
}
