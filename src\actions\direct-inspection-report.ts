"use server";

import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";

export async function getInspectionReportData(inspectionId: string) {
  try {
    // Buscar os dados da inspeção com todos os relacionamentos necessários
    const inspection = await prisma.inspectionParameter.findUnique({
      where: { id: inspectionId },
      include: {
        photos: {
          include: {
            file: true,
          },
        },
        laborEquipament: {
          include: {
            labor: true,
          },
        },
        proposal: {
          include: {
            customer: true,
          },
        },
      },
    });

    if (!inspection) {
      throw new Error("Inspeção não encontrada");
    }

    return {
      inspection: parseObject(inspection),
      proposal: parseObject(inspection.proposal),
      customer: parseObject(inspection.proposal.customer),
    };
  } catch (error) {
    console.error("Erro ao buscar dados para o relatório:", error);
    throw error;
  }
}
