"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { parseObject } from "@/src/lib/utils";
import { File } from "@/src/types/core/file";
import { generateAndStoreThumbnail } from "@/src/lib/thumbnail";

export async function loadFile(path: string) {
  try {
    return await storageProvider.get(path);
  } catch (error) {
    console.error(error);
  }
}

export async function saveFile(file: File) {
  try {
    console.log(
      `Iniciando upload do arquivo: ${file.name}, caminho: ${file.path}`
    );
    const url = await storageProvider.upload(file);
    console.log(`Upload concluído, URL gerada: ${url}`);

    const createdFile = await prisma.file.create({
      data: {
        name: file.name,
        path: url,
        type: file.type,
        size: file.size,
      },
    });

    console.log(`Arquivo salvo no banco de dados com ID: ${createdFile.id}`);

    // Gerar miniatura para imagens
    if (file.type.startsWith("image/")) {
      const thumbnailPath = await generateAndStoreThumbnail(
        createdFile.id,
        url
      );
      if (thumbnailPath) {
        // Atualizar o registro com o caminho da miniatura
        await prisma.file.update({
          where: { id: createdFile.id },
          data: { thumbnailPath },
        });
        console.log(`Miniatura gerada e salva: ${thumbnailPath}`);
      }
    }

    return parseObject(createdFile) as File;
  } catch (error) {
    console.error("Erro ao salvar arquivo:", error);
    throw new Error("Erro ao salvar arquivo");
  }
}

export async function saveFiles(files: File[]) {
  try {
    const urls = await storageProvider.uploadFiles(files);

    files.forEach((file, index) => {
      file.path = urls[index];
    });

    await prisma.file.createMany({
      data: files.map((file) => ({
        name: file.name,
        path: file.path,
        type: file.type,
        size: file.size,
      })),
    });

    const createdFiles = await prisma.file.findMany({
      where: {
        path: {
          in: files.map((file) => file.path),
        },
      },
    });

    // Gerar miniaturas para imagens
    const imageFiles = createdFiles.filter((file) =>
      file.type.startsWith("image/")
    );
    if (imageFiles.length > 0) {
      const thumbnailPromises = imageFiles.map((file) =>
        generateAndStoreThumbnail(file.id, file.path)
      );
      await Promise.all(thumbnailPromises);
      console.log(`Miniaturas geradas para ${imageFiles.length} imagens`);
    }

    return parseObject(createdFiles) as File[];
  } catch (error) {
    console.error(error);
  }
}

export async function removeFile(file: File) {
  try {
    // Verificar se o arquivo é válido
    if (!file || !file.id || !file.path) {
      console.log("Arquivo inválido para remover");
      return { success: true, message: "Arquivo inválido para remover" };
    }

    // Buscar informações completas do arquivo
    const fileRecord = await prisma.file.findUnique({
      where: { id: file.id },
    });

    // Remover arquivo do storage
    await storageProvider.delete(file.path);

    // Remover miniatura se existir
    if (fileRecord?.thumbnailPath) {
      try {
        await storageProvider.delete(fileRecord.thumbnailPath);
        console.log(`Miniatura removida: ${fileRecord.thumbnailPath}`);
      } catch (thumbnailError) {
        console.error("Erro ao remover miniatura:", thumbnailError);
      }
    }

    // Remover registro do banco de dados
    await prisma.file.delete({ where: { id: file.id } });

    return { success: true, message: "Arquivo removido com sucesso" };
  } catch (error) {
    console.error("Erro ao remover arquivo:", error);
    return { success: false, message: "Erro ao remover arquivo", error };
  }
}

export async function removeFiles(files: File[]) {
  try {
    // Verificar se há arquivos para remover
    if (!files || files.length === 0) {
      console.log("Nenhum arquivo para remover");
      return { success: true, message: "Nenhum arquivo para remover" };
    }

    // Filtrar arquivos válidos (com id e path)
    const validFiles = files.filter((file) => file && file.id && file.path);

    if (validFiles.length === 0) {
      console.log("Nenhum arquivo válido para remover");
      return { success: true, message: "Nenhum arquivo válido para remover" };
    }

    // Buscar informações completas dos arquivos para obter caminhos das miniaturas
    const fileRecords = await prisma.file.findMany({
      where: { id: { in: validFiles.map((file) => file.id) } },
    });

    // Remover arquivos do storage
    await storageProvider.deleteMany(validFiles.map((file) => file.path));

    // Remover miniaturas se existirem
    const thumbnailPaths = fileRecords
      .filter((file) => file.thumbnailPath)
      .map((file) => file.thumbnailPath as string);

    if (thumbnailPaths.length > 0) {
      try {
        await storageProvider.deleteMany(thumbnailPaths);
        console.log(`${thumbnailPaths.length} miniaturas removidas`);
      } catch (thumbnailError) {
        console.error("Erro ao remover miniaturas:", thumbnailError);
      }
    }

    // Remover registros do banco de dados
    await prisma.file.deleteMany({
      where: { id: { in: validFiles.map((file) => file.id) } },
    });

    return {
      success: true,
      message: `${validFiles.length} arquivos removidos com sucesso`,
    };
  } catch (error) {
    console.error("Erro ao remover arquivos:", error);
    return { success: false, message: "Erro ao remover arquivos", error };
  }
}
