"use server";

import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import {
  InspectionParameter,
  Photo,
} from "@/src/types/core/inspection-paramenters";
import { InspectionParameterSchema } from "@/src/app/views/crm/proposals/inspection-parameters/form/schemas/inspection-parameters.schema";
import { removeFiles, saveFiles } from "./files";
import { generateReport } from "./inspection-report";
import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";

export async function loadInspectionParameters(
  proposalId: string,
  page = 1,
  pageSize = 10,
  search = ""
) {
  try {
    const skip = (page - 1) * pageSize;

    const where = {
      proposalId,
      ...(search
        ? {
            OR: [
              {
                technicalData: { contains: search, mode: "insensitive" as any },
              },
              { observation: { contains: search, mode: "insensitive" as any } },
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.inspectionParameter.count({ where }),
      prisma.inspectionParameter.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { inspectionDate: "desc" },
      }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    return {
      items: parseObject(items) as InspectionParameter[],
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
      },
    };
  } catch (error) {
    console.error(error);
    return {
      items: [],
      pagination: {
        page,
        pageSize,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

export async function findInspectionParameter(id: string) {
  try {
    const data = await prisma.inspectionParameter.findUnique({
      where: { id },
      include: {
        photos: {
          include: {
            file: true,
          },
        },
        laborEquipament: {
          include: {
            labor: true,
          },
        },
      },
    });
    return parseObject(data) as InspectionParameter;
  } catch (error) {
    console.error(error);
  }
}

export async function getNextInspectionNumber(proposalId: string) {
  try {
    // Buscar a inspeção com o maior número para esta proposta
    const highestNumberInspection = await prisma.inspectionParameter.findFirst({
      where: { proposalId },
      orderBy: { numberInspection: "desc" },
      select: { numberInspection: true },
    });

    // Se não houver inspeções ou o número for nulo, retornar 1, caso contrário incrementar o maior número
    return (highestNumberInspection?.numberInspection || 0) + 1;
  } catch (error) {
    console.error("Erro ao obter próximo número de inspeção:", error);
    return 1; // Em caso de erro, começar do 1
  }
}

export async function findLaborEquipamentByInspectionParameterId(
  inspectionParameterId: string
) {
  try {
    const data = await prisma.laborEquipmentAmount.findMany({
      where: {
        inspectionParameterId,
      },
      include: {
        labor: true,
      },
      orderBy: {
        labor: {
          name: "asc",
        },
      },
    });
    return data;
  } catch (error) {
    console.error(error);
  }
}

export async function saveLaborEquipamentByInspectionParameter(
  inspectionParameterId: string,
  laborEquipament: any[]
) {
  try {
    // Primeiro, buscamos todos os registros existentes para este parâmetro de inspeção
    const existingItems = await prisma.laborEquipmentAmount.findMany({
      where: {
        inspectionParameterId,
      },
    });

    // Excluímos os registros existentes
    if (existingItems.length > 0) {
      await prisma.laborEquipmentAmount.deleteMany({
        where: {
          id: {
            in: existingItems.map((item) => item.id),
          },
        },
      });
    }

    // Em seguida, criamos novos registros para cada item de mão de obra/equipamento
    if (laborEquipament && laborEquipament.length > 0) {
      const createPromises = laborEquipament.map((item) =>
        prisma.laborEquipmentAmount.create({
          data: {
            laborId: item.laborId,
            amount: item.amount || 1,
            inspectionParameterId,
          },
        })
      );

      await Promise.all(createPromises);
    }

    return { success: true };
  } catch (error) {
    console.error(error);
    return { success: false, error };
  }
}

export async function saveInspectionParameter(
  inspectionParameter: InspectionParameterSchema,
  photos: Photo[]
) {
  try {
    const { proposalId, inspectionDate, observation, technicalData, id } =
      inspectionParameter;

    const where = { id };

    const include = {
      photos: {
        include: { file: true },
      },
      laborEquipament: {
        include: {
          labor: true,
        },
      },
    };

    // Se for uma nova inspeção, obter o próximo número de inspeção
    let nextNumber = inspectionParameter.numberInspection;
    if (!id) {
      nextNumber = await getNextInspectionNumber(proposalId!);
    }

    const data = {
      inspectionDate,
      observation,
      technicalData,
      numberInspection: nextNumber,
      proposalId: proposalId!,
    };

    let updatedInspectionParameter = id
      ? await prisma.inspectionParameter.update({
          where,
          data,
          include,
        })
      : await prisma.inspectionParameter.create({
          data,
          include,
        });

    const savedPhotoIdsToPersist = photos.map((photo) => photo.id);

    const savedPhotos = updatedInspectionParameter.photos;

    const photosToDelete = savedPhotos.filter(
      (photo) => !savedPhotoIdsToPersist.includes(photo.id)
    );

    if (photosToDelete.length) {
      updatedInspectionParameter = (await prisma.inspectionParameter.update({
        where: { id: updatedInspectionParameter.id },
        data: {
          photos: {
            disconnect: photosToDelete,
          },
        },
      })) as any;

      await removeFiles(photosToDelete.map((photo) => photo.file!));
    }

    // Atualizar a ordem das fotos existentes
    const photosToUpdate = photos.filter(
      (photo) => photo.id && typeof photo.order === "number"
    );

    if (photosToUpdate.length) {
      const updatePromises = photosToUpdate.map((photo) =>
        prisma.photo.update({
          where: { id: photo.id },
          data: { order: photo.order },
        })
      );

      await Promise.all(updatePromises);
    }

    const photosToCreate = photos.filter((photo) => !photo.id);

    if (photosToCreate.length) {
      const createdPhotos = await prisma.photo.createManyAndReturn({
        data: photosToCreate.map((photo) => ({
          inspectionParameterId: updatedInspectionParameter.id,
          description: photo.description,
          order: photo.order || 0,
        })),
      });

      photosToCreate.forEach((photo, index) => {
        photo.file.path = `photos/${createdPhotos[index].id}`;
        photo.file.name = (photo.file as any)._name;
        photo.file.size = (photo.file as any)._size;
        photo.file.type = (photo.file as any)._type;

        delete (photo.file as any)._name;
        delete (photo.file as any)._size;
        delete (photo.file as any)._type;
      });

      const files = await saveFiles(photosToCreate.map((photo) => photo.file!));

      if (files?.length && files.length == createdPhotos.length) {
        createdPhotos.forEach((photo, index) => {
          photo.fileId = files[index].id;
        });

        const photosWithFilesUpdate = createdPhotos.map((photo) =>
          prisma.photo.update({
            where: { id: photo.id },
            data: {
              fileId: photo.fileId,
            },
          })
        );

        await Promise.all(photosWithFilesUpdate);
      }

      updatedInspectionParameter = (await prisma.inspectionParameter.findUnique(
        {
          where: { id: updatedInspectionParameter.id },
          include,
        }
      )) as any;
    }

    return parseObject(updatedInspectionParameter) as InspectionParameter;
  } catch (error) {
    console.error(error);
  }
}

export async function removeInspectionParameter(id: string) {
  try {
    const inspectionParameter = await prisma.inspectionParameter.findUnique({
      where: { id },
      include: {
        photos: {
          include: {
            file: true,
          },
        },
      },
    });

    if (inspectionParameter?.photos.length) {
      await prisma.inspectionParameter.update({
        where: { id },
        data: {
          photos: {
            disconnect: inspectionParameter.photos,
          },
        },
      });

      await removeFiles(inspectionParameter.photos.map((photo) => photo.file!));

      await prisma.photo.deleteMany({
        where: {
          inspectionParameterId: id,
        },
      });
    }

    await prisma.inspectionParameter.delete({ where: { id } });

    return { message: "Parâmetro de inspeção removido com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function removeInspectionParameters(proposalId: string) {
  try {
    const inspectionParameters = await prisma.inspectionParameter.findMany({
      where: { proposalId },
      include: {
        photos: {
          include: {
            file: true,
          },
        },
      },
    });

    if (inspectionParameters.length) {
      const ids = inspectionParameters.map(
        (inspectionParameter) => inspectionParameter.id
      );

      const photos = await prisma.photo.findMany({
        where: {
          inspectionParameterId: {
            in: ids,
          },
        },
        include: {
          file: true,
        },
      });

      const updates = ids.map((id) =>
        prisma.inspectionParameter.update({
          where: { id },
          data: { photos: { set: [] } },
        })
      );

      await Promise.all(updates);

      await removeFiles(photos.map((photo) => photo.file!));

      await prisma.photo.deleteMany({
        where: {
          inspectionParameterId: {
            in: ids,
          },
        },
      });

      await prisma.inspectionParameter.deleteMany({
        where: { proposalId },
      });
    }

    return { message: "Parâmetros de inspeção removido com sucesso!" };
  } catch (error) {
    console.error(error);
  }
}

export async function generateInspectionReport(
  reportTemplateId: string,
  params: any
) {
  try {
    const { proposalId, inspectionId } = params;

    if (!proposalId) {
      throw new Error("ProposalId é obrigatório");
    }

    const reportTemplate = await prisma.reportTemplate.findUnique({
      where: { id: reportTemplateId },
      include: { fileEditor: true },
    });

    const data = await generateReport(proposalId, inspectionId);

    const fileName = `${data.dia}${data.mes}${data.ano}-${reportTemplate?.title}`;

    const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
      `${reportTemplate?.fileEditorId}`,
      data,
      fileName
    );

    if (!replacedFileEditor?.id) throw Error("Failed to save file editor");
    const fileEditorId = replacedFileEditor?.id;
    return {
      fileEditorId,
    };
  } catch (error) {
    console.error(error);
  }
}
