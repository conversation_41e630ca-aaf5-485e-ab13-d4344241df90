"use server";
import { prisma } from "@/src/lib/prisma";
import { auth } from "@/src/providers/auth";
import { Prisma } from "@prisma/client";

export async function requestOrganizationAccess(organizationId: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("User not authenticated");
    }

    const membership = await prisma.membership.create({
      data: {
        userId: session.user.id,
        organizationId: organizationId,
        role: "MEMBER",
        enabled: false,
      },
    });

    return membership;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function checkUserMembership() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("User not authenticated");
    }

    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id,
      },
      include: {
        organization: true,
      },
    });

    return membership;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function getCurrentMembership() {
  try {
    const session = await auth();
    if (!session?.membership) {
      return null;
    }

    return session.membership;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function getFirstEnabledMemberFromUserId(id: string) {
  return prisma.membership.findFirst({
    where: {
      userId: id,
      enabled: true,
    },
  });
}

export async function loadOrganizationMembers(
  organizationId: string,
  page: number = 1,
  pageSize: number = 10,
  search: string = ""
) {
  try {
    const skip = (page - 1) * pageSize;

    const where = {
      organizationId,
      ...(search
        ? {
            user: {
              OR: [
                { name: { contains: search, mode: "insensitive" } },
                { email: { contains: search, mode: "insensitive" } },
              ],
            },
          }
        : {}),
    } satisfies Prisma.MembershipWhereInput;

    const [total, members] = await Promise.all([
      prisma.membership.count({
        where,
      }),
      prisma.membership.findMany({
        where,
        include: { user: true },
        orderBy: { createdAt: "desc" },
        skip,
        take: pageSize,
      }),
    ]);

    return {
      data: members,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function toggleMemberStatus(membershipId: string) {
  try {
    const session = await auth();
    if (!session?.membership?.organizationId) {
      throw new Error("Not authorized");
    }

    // Get current membership
    const membership = await prisma.membership.findUnique({
      where: { id: membershipId },
    });

    if (
      !membership ||
      membership.organizationId !== session.membership.organizationId
    ) {
      throw new Error("Membership not found or access denied");
    }

    // Toggle enabled status
    const updatedMembership = await prisma.membership.update({
      where: { id: membershipId },
      data: {
        enabled: !membership.enabled,
      },
      include: {
        user: true,
      },
    });

    return updatedMembership;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function setMemberOrganization(organizationId: string) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("User not authenticated");
    }

    const membership = await prisma.membership.updateMany({
      data: {
        organizationId,
      },
      where: {
        userId: session?.user?.id,
        role: "OWNER",
      },
    });

    return membership;
  } catch (error) {
    console.error(error);
    throw error;
  }
}
