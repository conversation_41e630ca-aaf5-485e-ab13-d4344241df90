"use server";

import { prisma } from "@/src/lib/prisma";
import { formatDate } from "@/src/lib/utils";
import { replaceFromFileEditorIdAndVariables } from "@/src/helpers/file-editor";
import { MINIO_BUCKET_NAME, MINIO_ENDPOINT } from "@/src/lib/env/variables";

export async function generateProjectReport(
  reportTemplateId: string,
  params: any
) {
  try {
    const { proposalId } = params;

    if (!proposalId) {
      throw new Error("ProposalId é obrigatório");
    }

    const reportTemplate = await prisma.reportTemplate.findUnique({
      where: { id: reportTemplateId },
      include: { fileEditor: true },
    });

    const data = await generateReport(proposalId);

    const fileName = `${data.dia}${data.mes}${data.ano}-${reportTemplate?.title}`;

    const replacedFileEditor = await replaceFromFileEditorIdAndVariables(
      `${reportTemplate?.fileEditorId}`,
      data,
      fileName
    );

    if (!replacedFileEditor?.id) throw Error("Failed to save file editor");
    const fileEditorId = replacedFileEditor?.id;
    return {
      fileEditorId,
    };
  } catch (error) {
    console.error("Error generating project report:", error);
    throw error;
  }
}

export async function generateReport(proposalId: string) {
  try {
    // Buscar a proposta com o arquivo principal
    const data = await prisma.proposal.findUnique({
      where: {
        id: proposalId,
      },
      include: {
        customer: true,
        file: true,
        // Buscar parâmetros de inspeção e fotos relacionadas
        inspectionParameters: {
          include: {
            photos: {
              include: {
                file: true,
              },
            },
          },
        },
      },
    });

    if (!data) {
      throw new Error("Proposal not found");
    }

    // Gerar texto do relatório com base nos dados do projeto
    const reportText = await generateReportText(data);

    // Gerar sugestões com base nos dados do projeto
    const suggestions = await generateSuggestions(data);

    // Log para depuração - verificar arquivos encontrados
    console.log("Arquivos do projeto:", {
      mainFile: data.file ? data.file.path : "Nenhum arquivo principal",
      inspectionPhotos: data.inspectionParameters
        ? data.inspectionParameters.flatMap((param) =>
            param.photos.map((photo) => photo.file?.path || "Sem caminho")
          )
        : "Nenhuma foto de inspeção",
    });

    // Buscar todas as inspeções de parâmetros para gerar o resumo de atividades
    const inspectionParameters = await prisma.inspectionParameter.findMany({
      where: {
        proposalId: proposalId,
      },
      include: {
        laborEquipament: {
          include: {
            labor: true,
          },
        },
      },
      orderBy: {
        inspectionDate: "asc",
      },
    });

    // Formatar dados técnicos das inspeções para o resumo de atividades
    const resumoAtividades = inspectionParameters
      .map((item, index) => {
        const date = formatDate(item.inspectionDate, "DATE");
        return `${index + 1}. ${date} - ${item.technicalData}`;
      })
      .join("\n");

    // Consolidar todos os dados de mão de obra e equipamentos de todas as inspeções
    const allLaborEquipment = inspectionParameters.flatMap(
      (param) => param.laborEquipament || []
    );

    // Separar mão de obra e equipamentos
    const laborData = allLaborEquipment.filter(
      (item) => item.labor.type === "LABOR"
    );
    const equipmentData = allLaborEquipment.filter(
      (item) => item.labor.type === "EQUIPAMENT"
    );

    // Consolidar quantidades por tipo (somar quantidades de itens iguais)
    const consolidatedLabor = laborData.reduce((acc, item) => {
      const key = item.labor.name;
      if (acc[key]) {
        acc[key].amount += item.amount || 0;
      } else {
        acc[key] = {
          name: item.labor.name,
          description: item.labor.description,
          amount: item.amount || 0,
          type: "LABOR",
        };
      }
      return acc;
    }, {} as Record<string, any>);

    const consolidatedEquipment = equipmentData.reduce((acc, item) => {
      const key = item.labor.name;
      if (acc[key]) {
        acc[key].amount += item.amount || 0;
      } else {
        acc[key] = {
          name: item.labor.name,
          description: item.labor.description,
          amount: item.amount || 0,
          type: "EQUIPAMENT",
        };
      }
      return acc;
    }, {} as Record<string, any>);

    // Formatar listagem de mão de obra e equipamentos
    const laborList = Object.values(consolidatedLabor)
      .map(
        (item: any) =>
          `• ${item.name}: ${item.amount} ${
            item.description ? `(${item.description})` : ""
          }`
      )
      .join("\n");

    const equipmentList = Object.values(consolidatedEquipment)
      .map(
        (item: any) =>
          `• ${item.name}: ${item.amount} ${
            item.description ? `(${item.description})` : ""
          }`
      )
      .join("\n");

    const maoObraEquipamentos =
      [
        laborList ? `Mão de Obra:\n${laborList}` : "",
        equipmentList ? `Equipamentos:\n${equipmentList}` : "",
      ]
        .filter(Boolean)
        .join("\n\n") ||
      "Nenhuma mão de obra ou equipamento registrado para este projeto.";

    const variables: any = {
      obra: data.name,
      nome: data.customer.name,
      cnpj: data.customer.document,
      cliente: data.customer.name,
      endereco: data.address ?? "",
      cidade: data.city ?? "",
      estado: data.state ?? "",
      cep: data.cep,
      relatorio: reportText,
      sugestoes: suggestions,
      resumo_atividades:
        resumoAtividades ||
        "Nenhuma atividade de inspeção registrada para este projeto.",
      mao_obra_equipamentos: maoObraEquipamentos,
      dataHoje: formatDate(new Date(), "DATE"),
      dia: new Date(data.startDate).getDate(),
      mes: new Date(data.startDate).toLocaleString("pt-BR", { month: "long" }),
      ano: new Date(data.startDate).getFullYear(),
      diaFinal: new Date(data.endDate).getDate(),
      mesFinal: new Date(data.endDate).toLocaleString("pt-BR", {
        month: "long",
      }),
      anoFinal: new Date(data.endDate).getFullYear(),
      prazoContratual: `${Math.ceil(
        (new Date(data.endDate).getTime() -
          new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoDecorrido: `${Math.ceil(
        (new Date().getTime() - new Date(data.startDate).getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      prazoVencer: `${Math.ceil(
        (new Date(data.endDate).getTime() - new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )} dias`,
      items: [
        // Incluir o arquivo principal do projeto, se existir
        ...(data.file
          ? [
              {
                image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${data.file.path}`,
                numero: 1,
                legenda: "Arquivo principal do projeto",
              },
            ]
          : []),
        // Incluir todas as fotos dos parâmetros de inspeção
        ...data.inspectionParameters.flatMap((param, paramIndex) =>
          param.photos
            .filter((photo) => photo.file && photo.file.path) // Filtrar apenas fotos com arquivo válido
            .map((photo, photoIndex) => ({
              image: `${MINIO_ENDPOINT}/${MINIO_BUCKET_NAME}/${photo?.file?.path}`,
              numero: data.file
                ? paramIndex + photoIndex + 2
                : paramIndex + photoIndex + 1,
              legenda:
                photo.description ||
                `Imagem ${paramIndex + photoIndex + 1} do projeto`,
            }))
        ),
      ],
      valorTotal: formatCurrency(Number(data.workTotalCost || 0)),
      valorServico: formatCurrency(Number(data.budget || 0)),
      tipoServico: formatServiceType(data.serviceType || "PROJECT"),
      descricaoServico: data.customService || "Projeto de engenharia",
    };

    // Log para depuração - verificar as imagens que serão incluídas no relatório
    console.log("Items para o relatório:", variables.items);

    return variables;
  } catch (err) {
    console.error(err);
    throw err;
  }
}

function formatCurrency(value: number): string {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(value);
}

function formatServiceType(type: string): string {
  const types: Record<string, string> = {
    PROJECT: "Projeto",
    INSPECAO: "Inspeção",
    CONSULTORIA: "Consultoria",
    FISCALIZACAO: "Fiscalização",
    GERENCIAMENTO: "Gerenciamento",
  };

  return types[type] || type;
}

async function generateReportText(proposal: any): Promise<string> {
  const prompt = `Você é um engenheiro civil especialista em projetos de engenharia.
  Gere um texto detalhado para um relatório de projeto com base nas seguintes informações:

  Nome do projeto: ${proposal.name}
  Cliente: ${proposal.customer.name}
  Endereço: ${proposal.address}, ${proposal.city}, ${proposal.state}
  Tipo de serviço: ${formatServiceType(proposal.serviceType)}
  Descrição do serviço: ${proposal.customService || "Projeto de engenharia"}
  Valor do serviço: ${formatCurrency(proposal.serviceCost || 0)}
  Valor total da obra: ${formatCurrency(proposal.workTotalCost || 0)}
  Data de início: ${formatDate(proposal.startDate, "DATE")}
  Data de término prevista: ${formatDate(proposal.endDate, "DATE")}

  Gere um texto com vários parágrafos descrevendo o projeto com riqueza de detalhes utilizando termos técnicos da área de engenharia civil.`;

  try {
    const response = await fetch("https://api.openai.com/v1/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo-instruct",
        prompt,
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].text.trim();
  } catch (error) {
    console.error("Error generating report text:", error);
    return "Não foi possível gerar o texto do relatório. Por favor, tente novamente mais tarde.";
  }
}

async function generateSuggestions(proposal: any): Promise<string> {
  const prompt = `Você é um engenheiro civil especialista em projetos de engenharia.
  Com base nas seguintes informações de um projeto, sugira recomendações técnicas e próximos passos:

  Nome do projeto: ${proposal.name}
  Cliente: ${proposal.customer.name}
  Endereço: ${proposal.address}, ${proposal.city}, ${proposal.state}
  Tipo de serviço: ${formatServiceType(proposal.serviceType)}
  Descrição do serviço: ${proposal.customService || "Projeto de engenharia"}

  Faça um breve parágrafo resumindo as recomendações e, em seguida, crie uma sequência de bullets descrevendo cada recomendação detalhadamente. Finalize com uma conclusão.`;

  try {
    const response = await fetch("https://api.openai.com/v1/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo-instruct",
        prompt,
        max_tokens: 600,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].text.trim();
  } catch (error) {
    console.error("Error generating suggestions:", error);
    return "Não foi possível gerar as sugestões. Por favor, tente novamente mais tarde.";
  }
}
