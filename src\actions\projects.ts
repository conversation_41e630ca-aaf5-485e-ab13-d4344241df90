"use server";
import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import { File } from "@/src/types/core/file";
import { Project } from "@/src/types/core/project";
import { ProjectSchema } from "@/src/app/views/crm/customers/_schemas/project.schema";
import { removeFiles, saveFile } from "./files";

export async function loadProjects(customerId: string) {
  try {
    const data = await prisma.project.findMany({
      where: { customerId },
      include: { projectFiles: { include: { file: true } } },
    });

    // Garantir que os dados sejam serializados corretamente
    try {
      // Converter manualmente os campos problemáticos para garantir
      const formattedData = data.map((project) => {
        let area = 0;
        let contractValue = 0;

        // Converter área para número
        try {
          if (project.area) {
            area = Number(project.area.toString());
          }
        } catch (e) {
          console.error("Erro ao converter área:", e);
        }

        // Converter valor do contrato para número
        try {
          if (project.contractValue) {
            contractValue = Number(project.contractValue.toString());
          }
        } catch (e) {
          console.error("Erro ao converter valor do contrato:", e);
        }

        return {
          ...project,
          area,
          contractValue,
          startDate: project.startDate ? project.startDate.toISOString() : null,
          endDate: project.endDate ? project.endDate.toISOString() : null,
          files: project.projectFiles.map((pf) => pf.file),
        };
      });

      return parseObject(formattedData);
    } catch (e) {
      console.error("Erro ao serializar projetos:", e);
      // Fallback extremo: retornar apenas os IDs e nomes, mas incluir informações dos arquivos se disponível
      return data.map((project) => ({
        id: project.id,
        project: project.project,
        customerId: project.customerId,
        area: 0,
        contractValue: 0,
        serviceType: project.serviceType || "",
        files: project.projectFiles?.map((pf) => pf.file) || [],
        startDate: null,
        endDate: null,
      }));
    }
  } catch (error) {
    console.error("Erro ao carregar projetos:", error);
    return [];
  }
}

export async function saveProject(project: ProjectSchema & { files?: File[] }) {
  try {
    const {
      id,
      files, // agora é um array de arquivos
      customerId,
      project: projectName,
      startDate,
      endDate,
      area,
      contractValue,
      serviceType,
    } = project;

    console.log("Salvando projeto:", {
      id,
      files: files?.length || 0,
      projectName,
    });

    // Verificar se é uma atualização ou criação
    let projectRecord: any;
    if (id) {
      // Atualização de projeto existente
      projectRecord = await prisma.project.update({
        where: { id },
        data: {
          project: projectName,
          startDate,
          endDate,
          area: area ? area : undefined,
          contractValue: contractValue ? contractValue : undefined,
          serviceType,
        },
      });

      // Se arquivos foram fornecidos, substituir todos os arquivos
      if (files && Array.isArray(files)) {
        console.log("Substituindo arquivos do projeto");
        // Remover associações antigas de arquivos apenas se novos arquivos foram fornecidos
        await prisma.projectFile.deleteMany({ where: { projectId: id } });

        // Processar arquivos: separar existentes dos novos
        const filePath = `customers/${customerId}/projects/${projectRecord.id}`;
        for (const file of files) {
          // Se o arquivo já tem ID, é um arquivo existente - recriar a associação
          if (file.id) {
            console.log(
              "Recriando associação para arquivo existente:",
              file.id
            );
            await prisma.projectFile.create({
              data: {
                projectId: projectRecord.id,
                fileId: file.id,
              },
            });
          }
          // Se não tem ID, é um arquivo novo - fazer upload
          else if (file.buffer) {
            console.log("Fazendo upload de novo arquivo:", file.name);
            const createdFile = await saveFile({
              name: file.name,
              path: filePath,
              type: file.type,
              size: file.size,
              buffer: file.buffer,
            } as File);
            if (createdFile) {
              // Cria relação N:N
              await prisma.projectFile.create({
                data: {
                  projectId: projectRecord.id,
                  fileId: createdFile.id,
                },
              });
            }
          }
        }
      }
    } else {
      // Criação de novo projeto
      projectRecord = await prisma.project.create({
        data: {
          project: projectName,
          customerId: customerId!,
          startDate,
          endDate,
          area: area ? area : undefined,
          contractValue: contractValue ? contractValue : undefined,
          serviceType,
        },
      });

      // Se tiver arquivos para salvar
      if (files && Array.isArray(files) && files.length > 0) {
        const filePath = `customers/${customerId}/projects/${projectRecord.id}`;
        for (const file of files) {
          const createdFile = await saveFile({
            name: file.name,
            path: filePath,
            type: file.type,
            size: file.size,
            buffer: file.buffer,
          } as File);
          if (createdFile) {
            // Cria relação N:N
            await prisma.projectFile.create({
              data: {
                projectId: projectRecord.id,
                fileId: createdFile.id,
              },
            });
          }
        }
      }
    }

    // Buscar projeto com arquivos associados
    const projectWithFiles = await prisma.project.findUnique({
      where: { id: projectRecord.id },
      include: { projectFiles: { include: { file: true } } },
    });

    // Garantir que os dados sejam serializados corretamente
    try {
      return parseObject({
        ...projectWithFiles,
        files: projectWithFiles?.projectFiles.map((pf) => pf.file) || [],
      }) as Project;
    } catch (e) {
      console.error("Erro ao serializar projeto:", e);
      // Fallback: converter manualmente os campos problemáticos
      return {
        ...projectWithFiles,
        area: projectWithFiles?.area
          ? Number(projectWithFiles.area.toString())
          : 0,
        contractValue: projectWithFiles?.contractValue
          ? Number(projectWithFiles?.contractValue.toString())
          : 0,
        serviceType: projectWithFiles?.serviceType || "",
        files: projectWithFiles?.projectFiles.map((pf) => pf.file) || [],
      } as Project;
    }
  } catch (error) {
    console.error("Erro ao salvar projeto:", error);
    throw new Error("Erro ao salvar projeto");
  }
}

export async function removeProject(id: string) {
  try {
    const project = await prisma.project.findUnique({
      where: { id },
      include: { projectFiles: { include: { file: true } } },
    });

    // Remover os arquivos do bucket se existirem
    if (project?.projectFiles && project.projectFiles.length > 0) {
      const filesToRemove = project.projectFiles.map((pf) => pf.file);
      await removeFiles(filesToRemove);
      console.log(`Arquivos do projeto removidos`);
    }

    // Remover as relações ProjectFile
    await prisma.projectFile.deleteMany({ where: { projectId: id } });

    // Remover o projeto do banco de dados
    await prisma.project.delete({ where: { id } });

    return { message: "Projeto deletado com sucesso!" };
  } catch (error) {
    console.error("Erro ao remover projeto:", error);
    return { error: true, message: "Erro ao remover projeto" };
  }
}
