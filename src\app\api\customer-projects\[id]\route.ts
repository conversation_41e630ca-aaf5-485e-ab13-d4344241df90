import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { parseObject } from "@/lib/utils";
import { ProposalSituation } from "@prisma/client";

// Função auxiliar para processar datas com segurança
function safelyProcessDate(
  dateValue: any,
  errorContext: string = "data"
): string | null {
  if (!dateValue) return null;
  try {
    // Se for um objeto Date do Prisma
    if (dateValue && typeof dateValue === "object" && dateValue.toISOString) {
      return dateValue.toISOString();
    }

    // Se for uma string de data
    if (typeof dateValue === "string") {
      const testDate = new Date(dateValue);
      if (!isNaN(testDate.getTime())) {
        return dateValue;
      }
    }

    // Se for um objeto Date do JavaScript
    if (dateValue instanceof Date) {
      if (!isNaN(dateValue.getTime())) {
        return dateValue.toISOString();
      }
    }

    // Se for um timestamp
    if (typeof dateValue === "number") {
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return date.toISOString();
      }
    }

    // Se chegou aqui, não conseguiu processar a data
    console.warn(`Valor de data inválido para ${errorContext}:`, dateValue);
    return null;
  } catch (e) {
    console.error(`Erro ao processar ${errorContext}:`, e);
    return null;
  }
}

// Função auxiliar para converter data com segurança
function safeDate(date: any, fallback: string | null = null): string | null {
  if (!date) return fallback;
  try {
    const d = new Date(date);
    return isNaN(d.getTime()) ? fallback : d.toISOString();
  } catch (e) {
    console.error("Erro ao converter data:", e);
    return fallback;
  }
}

// Função auxiliar para processar valores numéricos com segurança
function safeNumber(value: any, fallback: number = 0): number {
  if (value === null || value === undefined) return fallback;

  try {
    // Se já for um número, retornar diretamente
    if (typeof value === "number" && !isNaN(value)) {
      return value;
    }

    // Tentar converter para número
    const num = Number(value);
    return isNaN(num) ? fallback : num;
  } catch (e) {
    console.error("Erro ao converter para número:", e);
    return fallback;
  }
}

export const dynamic = "force-dynamic";

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = params.id;
    const { searchParams } = new URL(request.url);
    const serviceType = searchParams.get("serviceType") || undefined;

    if (!customerId) {
      return NextResponse.json(
        { error: "ID do cliente não fornecido" },
        { status: 400 }
      );
    }

    console.log(
      `Buscando projetos concluídos para o cliente: ${customerId}, filtro por serviceType: ${
        serviceType || "nenhum"
      }`
    );

    // Buscar apenas propostas concluídas do cliente com arquivos relacionados
    const proposals = await prisma.proposal.findMany({
      where: {
        customerId,
        situation: "PROJECT_FINISHED", // Filtrar apenas projetos concluídos
        ...(serviceType && serviceType !== "ALL" ? { serviceType } : {}),
      },
      include: {
        file: true,
        fileEditor: true,
        contract: {
          include: {
            File: true,
            fileEditor: true,
          },
        },
        customer: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Buscar projetos do cliente com arquivos relacionados
    const projects = await prisma.project.findMany({
      where: {
        customerId,
        ...(serviceType && serviceType !== "ALL" ? { serviceType } : {}),
      },
      include: {
        projectFiles: { include: { file: true } },
        customer: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Converter as datas e valores numéricos com tratamento de erro
    const projectsWithFormattedDates = projects.map((project) => {
      // Log para depuração
      console.log(`Processando projeto ${project.id}:`);
      console.log("area original:", project.area, typeof project.area);
      console.log(
        "contractValue original:",
        project.contractValue,
        typeof project.contractValue
      );

      // Pega o primeiro arquivo (se houver) para manter compatibilidade com o frontend antigo
      const firstFile = project.projectFiles?.[0]?.file || null;

      return {
        ...project,
        // Adicionar flag para identificar projetos anteriores
        isPreviousProject: true,
        // Processar datas
        startDate: safeDate(project.startDate),
        endDate: safeDate(project.endDate),
        createdAt: safeDate(project.createdAt, new Date().toISOString()), // Usar data atual como fallback para createdAt
        updatedAt: safeDate(project.updatedAt, new Date().toISOString()), // Usar data atual como fallback para updatedAt

        // Processar valores numéricos
        area: safeNumber(project.area),
        contractValue: safeNumber(project.contractValue),
        // Usar contractValue como budget para manter consistência
        budget: safeNumber(project.contractValue),
        // Adicionar todos os arquivos do projeto
        files: project.projectFiles?.map((pf) => pf.file) || [],
        // Para compatibilidade, manter o campo file como o primeiro arquivo (ou null)
        file: firstFile,
      };
    });

    // Converter as datas e valores numéricos das propostas
    const proposalsWithFormattedDates = proposals.map((proposal) => {
      // Log para depuração
      console.log(`Processando proposta ${proposal.id}:`);
      console.log("area original:", proposal.area, typeof proposal.area);
      console.log("budget original:", proposal.budget, typeof proposal.budget);

      // Extrair o valor do budget para processamento
      const budgetValue =
        typeof proposal.budget === "bigint"
          ? Number(proposal.budget)
          : proposal.budget;

      // Log para depuração
      console.log(
        `Proposta ${proposal.id} - fileEditorId:`,
        proposal.fileEditorId
      );

      return {
        ...proposal,
        // Processar datas
        startDate: safeDate(proposal.startDate),
        endDate: safeDate(proposal.endDate),
        createdAt: safeDate(proposal.createdAt, new Date().toISOString()),
        updatedAt: safeDate(proposal.updatedAt, new Date().toISOString()),

        // Processar valores numéricos
        area: safeNumber(proposal.area),
        budget: safeNumber(budgetValue),

        // Garantir que fileEditorId seja incluído
        fileEditorId: proposal.fileEditorId,
      };
    });

    // Transformar os dados para o formato esperado pelo frontend
    const proposalResult = parseObject(proposalsWithFormattedDates);
    const projectResult = parseObject(projectsWithFormattedDates);

    console.log(
      `Encontrados ${proposals.length} propostas concluídas e ${projects.length} projetos anteriores`
    );

    // Log para depuração - verificar as datas dos projetos
    if (projectsWithFormattedDates.length > 0) {
      console.log("Exemplo de datas de um projeto anterior (após formatação):");
      console.log(
        "startDate:",
        projectsWithFormattedDates[0].startDate,
        typeof projectsWithFormattedDates[0].startDate
      );
      console.log(
        "endDate:",
        projectsWithFormattedDates[0].endDate,
        typeof projectsWithFormattedDates[0].endDate
      );
      console.log(
        "createdAt:",
        projectsWithFormattedDates[0].createdAt,
        typeof projectsWithFormattedDates[0].createdAt
      );
    }

    // Garantir que o resultado seja sempre um array e ajustar a estrutura dos dados
    const formattedProposals = Array.isArray(proposalResult)
      ? proposalResult.map((proposal) => {
          // Processar todas as datas da proposta usando a função auxiliar
          const processedProposal = {
            ...proposal,
            startDate: safelyProcessDate(
              proposal.startDate,
              "data de início da proposta"
            ),
            endDate: safelyProcessDate(
              proposal.endDate,
              "data de término da proposta"
            ),
            createdAt: safelyProcessDate(
              proposal.createdAt,
              "data de criação da proposta"
            ),
            updatedAt: safelyProcessDate(
              proposal.updatedAt,
              "data de atualização da proposta"
            ),
          };

          // Se o contrato existir, ajustar a estrutura para usar 'file' em vez de 'File'
          if (processedProposal.contract) {
            // Log para depuração
            console.log(`Contrato da proposta ${processedProposal.id}:`, {
              id: processedProposal.contract.id,
              fileEditorId: processedProposal.contract.fileEditor?.id,
              fileId: processedProposal.contract.fileId,
              hasFile: !!processedProposal.contract.File,
            });

            return {
              ...processedProposal,
              contract: {
                ...processedProposal.contract,
                file: processedProposal.contract.File,
                fileEditorId: processedProposal.contract.fileEditor?.id,
                File: undefined,
                fileEditor: undefined,
              },
            };
          }
          return processedProposal;
        })
      : [];

    // Converter projetos para o formato de propostas para exibição unificada
    const formattedProjects = Array.isArray(projectResult)
      ? projectResult.map((project) => {
          // Garantir que as datas sejam strings ou null para evitar problemas de serialização
          const startDate = safelyProcessDate(
            project.startDate,
            "data de início do projeto"
          );
          const endDate = safelyProcessDate(
            project.endDate,
            "data de término do projeto"
          );

          // Log para depuração - verificar as datas e valores processados
          console.log(`Projeto ${project.id} - Valores processados:`);
          console.log(
            "startDate original:",
            project.startDate,
            "-> processada:",
            startDate
          );
          console.log(
            "endDate original:",
            project.endDate,
            "-> processada:",
            endDate
          );
          console.log(
            "area original:",
            project.area,
            "-> processada:",
            safeNumber(project.area)
          );
          console.log(
            "contractValue original:",
            project.contractValue,
            "-> processada:",
            safeNumber(project.contractValue)
          );
          console.log(
            "files disponíveis:",
            project.files ? project.files.length : 0,
            "arquivos"
          );
          if (project.files && project.files.length > 0) {
            console.log(
              "Arquivos do projeto:",
              project.files.map((f: any) => ({ id: f.id, name: f.name }))
            );
          }

          return {
            id: project.id,
            name: `Projeto anterior: ${project.project}`,
            startDate: startDate,
            endDate: endDate,
            area: safeNumber(project.area),
            budget: safeNumber(project.contractValue),
            file: project.file,
            files: project.files, // Adicionar todos os arquivos do projeto
            customer: project.customer,
            customerId: project.customerId,
            serviceType: project.serviceType || "",
            createdAt: safelyProcessDate(
              project.createdAt,
              "data de criação do projeto"
            ),
            updatedAt: safelyProcessDate(
              project.updatedAt,
              "data de atualização do projeto"
            ),
            situation: ProposalSituation.PROJECT_FINISHED,
            isCustomProject: true, // Marcador para identificar que é um projeto personalizado
            // Adicionar campos obrigatórios da interface Proposal
            paymentCondition: "CASH",
            periodicity: "NONE",
            order: 0,
            serviceScopes: [],
            plannings: [],
            inspectionParameters: [],
          };
        })
      : [];

    // Combinar propostas e projetos em um único array
    const formattedResult = [...formattedProposals, ...formattedProjects];

    // Log final para verificar o resultado
    if (formattedResult.length > 0) {
      console.log("Exemplo de um item no resultado final:");
      const example = formattedResult[0];
      console.log("id:", example.id);
      console.log("name:", example.name);
      console.log("startDate:", example.startDate, typeof example.startDate);
      console.log("endDate:", example.endDate, typeof example.endDate);
      console.log("area:", example.area, typeof example.area);
      console.log(
        "budget/contractValue:",
        example.budget,
        typeof example.budget
      );
      console.log("isCustomProject:", example.isCustomProject);
      console.log(
        "files:",
        (example as any).files ? (example as any).files.length : 0,
        "arquivos"
      );
      if ((example as any).files && (example as any).files.length > 0) {
        console.log(
          "Detalhes dos arquivos:",
          (example as any).files.map((f: any) => ({ id: f.id, name: f.name }))
        );
      }
    }

    return NextResponse.json(formattedResult);
  } catch (error) {
    console.error("Erro ao buscar projetos do cliente:", error);

    // Extrair mensagem de erro mais detalhada
    let errorMessage = "Erro ao buscar projetos do cliente";
    if (error instanceof Error) {
      errorMessage += `: ${error.message}`;

      // Se for um erro de data inválida, adicionar mais detalhes
      if (
        error.message.includes("Invalid time value") ||
        error.message.includes("Invalid Date")
      ) {
        errorMessage =
          "Erro com datas inválidas nos projetos. Por favor, verifique as datas dos projetos.";
      }
    }

    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
