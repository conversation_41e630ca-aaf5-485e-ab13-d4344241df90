import { NextResponse } from "next/server";
import { loadCustomersPaginated } from "@/src/actions/customers";

export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get("search") || "";

    // Usamos a mesma função de paginação, mas com um tamanho de página maior
    // para buscar mais resultados quando o usuário digita
    const result = await loadCustomersPaginated(1, 20, search);

    // Formatar os resultados para o formato esperado pelo combobox
    const formattedResults = Array.isArray(result.data)
      ? result.data.map((customer) => ({
          label: customer.name,
          value: customer.id || "",
        }))
      : [];

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error("Erro ao buscar clientes:", error);
    return NextResponse.json({ error: "Erro ao buscar clientes" }, { status: 500 });
  }
}
