import { NextRequest, NextResponse } from "next/server";
import { GetObjectCommand } from "@aws-sdk/client-s3";
import { prisma } from "@/src/lib/prisma";
import { minioClient } from "@/src/lib/minio";

export async function GET(
  request: NextRequest,
  { params }: { params: { key: string } }
) {
  try {
    // Extrai a versão da query string
    const { searchParams } = new URL(request.url);
    const version = searchParams.get("version");

    // Busca o arquivo com metadados de versão
    const file = await prisma.fileEditor.findUnique({
      where: { key: params.key },
      select: {
        bucket: true,
        key: true,
        mimetype: true,
        filename: true,
        version: true
      },
    });

    if (!file) {
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    // Verifica se a versão solicitada corresponde à atual
    if (version) {
      const requestedVersion = BigInt(version);
      if (file.version === null || requestedVersion !== file.version) {
        return NextResponse.json(
          { error: "Version mismatch" },
          { status: 409 }
        );
      }
    }   

    const command = new GetObjectCommand({
      Bucket: file.bucket,
      Key: file.key,
    });

    const response = await minioClient.send(command);
    const chunks: Buffer[] = [];

    // @ts-expect-error - Body exists but TypeScript doesn't recognize it
    for await (const chunk of response.Body) {
      chunks.push(chunk);
    }

    const buffer = Buffer.concat(chunks as any);

    return new NextResponse(buffer, {
      headers: {
        "Content-Type": file.mimetype,
        "Content-Disposition": `inline; filename="${file.filename}"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "ETag": `"${file.version}"`,
        "Last-Modified": new Date().toUTCString()
      },
    });
  } catch (error) {
    console.error("Error serving file:", error);
    return NextResponse.json(
      { error: "Error serving file" }, 
      { status: 500 }
    );
  }
}