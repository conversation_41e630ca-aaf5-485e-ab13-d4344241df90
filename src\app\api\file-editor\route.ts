import { NextRequest, NextResponse } from 'next/server';
import { minioClient, bucketName } from '@/lib/minio';
import { v4 as uuidv4 } from 'uuid';
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { prisma } from '@/src/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const fileName = formData.get('fileName') as string;
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const key = uuidv4();
    const filename = fileName;
    // const filename = file.name;
    const mimetype = file.type;
    const size = file.size;

    const newVersion = Date.now();    
    
    // Upload to MinIO using S3 client
    await minioClient.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: buffer,
        ContentType: mimetype,
        Metadata: {
          version: newVersion.toString()  // Versão como metadado
        }
      })
    );

    // Save file reference to database
    const fileRecord = await prisma.fileEditor.create({
      data: {
        filename,
        mimetype,
        size,
        key,
        version: newVersion,
        bucket: `${bucketName}`,
      },
    });

    // Converter BigInt para string antes de retornar
    const safeResponse = {
      ...fileRecord,
      version: fileRecord.version?.toString(), // Conversão segura
      size: fileRecord.size.toString(), // Se size for BigInt também
      createdAt: fileRecord.createdAt.toISOString(),
      updatedAt: fileRecord.updatedAt.toISOString()
    };

    return NextResponse.json(safeResponse);

  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Error uploading file' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const files = await prisma.fileEditor.findMany({
      orderBy: { createdAt: 'desc' },
    });
    return NextResponse.json(files);
  } catch (error) {
    console.error('Error fetching files:', error);
    return NextResponse.json(
      { error: 'Error fetching files' },
      { status: 500 }
    );
  }
}