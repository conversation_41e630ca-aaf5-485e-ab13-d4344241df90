"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string } }
) {
  try {
    const fileId = decodeURIComponent(params.path);
    console.log(`Rota de API: Tentando obter arquivo com ID: ${fileId}`);

    // Buscar o arquivo no banco de dados primeiro para obter o caminho completo
    const fileRecord = await prisma.file.findUnique({
      where: { id: fileId },
    });

    if (!fileRecord) {
      console.log(`Registro de arquivo não encontrado no banco de dados: ${fileId}`);
      return NextResponse.json({ error: "File record not found." }, { status: 404 });
    }

    console.log(`Arquivo encontrado no banco de dados: ${fileRecord.name}, caminho: ${fileRecord.path}`);

    // Agora buscar o arquivo no storage usando o caminho completo
    const file = await storageProvider.get(fileRecord.path);
    if (!file || !file.stream) {
      console.log(`Arquivo não encontrado no storage: ${fileRecord.path}`);
      return NextResponse.json({ error: "File not found in storage." }, { status: 404 });
    }

    // Determina o Content-Type baseado no tipo do arquivo
    const contentDisposition =
      file.contentType?.includes("pdf") || file.contentType?.includes("image")
        ? "inline"
        : "attachment";

    console.log(`Retornando arquivo: ${fileRecord.name}, tipo: ${file.contentType}, disposição: ${contentDisposition}`);
    return new Response(file.stream, {
      headers: {
        "Content-Type": file.contentType || "application/octet-stream",
        "Content-Disposition": `${contentDisposition}; filename="${file.name}"`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
      },
    });
  } catch (error) {
    console.error("Error fetching file:", error);

    // Fornecer mensagem de erro mais detalhada
    let errorMessage = "Failed to fetch the file.";
    if (error instanceof Error) {
      errorMessage = `Error: ${error.message}`;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
