import { NextResponse } from "next/server";
import { prisma } from "@/src/lib/prisma";
import { parseObject } from "@/src/lib/utils";
import { getCurrentOrganization } from "@/src/lib/auth/get-current-organization";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = Number(searchParams.get("page")) || 1;
    const pageSize = Number(searchParams.get("pageSize")) || 10;
    const search = searchParams.get("search") || "";
    const customerId = searchParams.get("customerId") || undefined;

    const { organizationId } = await getCurrentOrganization();

    const skip = (page - 1) * pageSize;

    const where: any = {
      customer: {
        organizationId,
      },
      // Filtrar apenas contratos concluídos ou em andamento
      situation: {
        in: [
          "PROJECT_IN_PROGRESS",
          "PROJECT_FINISHED",
        ],
      },
      // Filtrar apenas contratos do tipo Projeto
      serviceType: {
        in: ["PROJECT"],
      },
      ...(customerId && { customerId }),
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" as any } },
              {
                customer: {
                  name: { contains: search, mode: "insensitive" as any },
                },
              },
              {
                customService: { contains: search, mode: "insensitive" as any },
              },
            ],
          }
        : {}),
    };

    const [total, items] = await Promise.all([
      prisma.proposal.count({ where }),
      prisma.proposal.findMany({
        where,
        skip,
        take: pageSize,
        include: {
          customer: true,
        },
        orderBy: { createdAt: "desc" },
      }),
    ]);

    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      data: parseObject(items),
      page,
      pageSize,
      total,
      totalPages,
    });
  } catch (error) {
    console.error("Error loading proposals with project type:", error);
    return NextResponse.json(
      { error: "Error loading proposals with project type" },
      { status: 500 }
    );
  }
}
