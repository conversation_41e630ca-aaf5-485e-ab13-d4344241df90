import { loadProposals } from "@/src/actions/proposals";
import { NextResponse } from "next/server";
import { ProposalFilters, ProposalSituation } from "@/src/types/core/proposal";

// Função para verificar se uma string é um valor válido de ProposalSituation
function isValidProposalSituation(situation: string): boolean {
  const validSituations: ProposalSituation[] = [
    "NEW",
    "UNDER_ANALYSIS",
    "PROPOSAL_SENT",
    "PROPOSAL_ACCEPTED",
    "SIGN_REQUESTED",
    "SIGNED",
    "PROJECT_IN_PROGRESS",
    "PROJECT_FINISHED",
    "LOST"
  ];

  return validSituations.includes(situation as ProposalSituation);
}

export const dynamic = "force-dynamic";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const searchParams = url.searchParams;

    // Extrair parâmetros de paginação e busca
    const page = searchParams.get("page") ? Number(searchParams.get("page")) : 1;
    const pageSize = searchParams.get("pageSize") ? Number(searchParams.get("pageSize")) : 10;
    const search = searchParams.get("search") || "";

    // Extrair situações (pode ser múltiplas)
    const situations = searchParams.getAll("situation");
    console.log("Situações recebidas:", situations);

    // Processar as situações para garantir que sejam valores válidos de ProposalSituation
    const validSituations: ProposalSituation[] = [];

    // Processar cada situação recebida
    situations.forEach(situation => {
      // Verificar se a situação contém vírgulas (formato separado por vírgulas)
      if (situation.includes(',')) {
        // Dividir a string em um array de situações
        const splitSituations = situation.split(',');
        console.log("Situações divididas:", splitSituations);

        // Adicionar cada situação válida ao array
        splitSituations.forEach(s => {
          if (s && isValidProposalSituation(s)) {
            validSituations.push(s as ProposalSituation);
          }
        });
      } else if (isValidProposalSituation(situation)) {
        // Se for uma única situação válida, adicionar ao array
        validSituations.push(situation as ProposalSituation);
      }
    });

    console.log("Situações válidas processadas:", validSituations);

    // Construir filtros
    const filters: ProposalFilters = {
      page,
      pageSize,
      search,
      ...(validSituations.length > 0 && { situation: validSituations }),
      ...(searchParams.get("customerId") && { customerId: searchParams.get("customerId") || undefined }),
      ...(searchParams.get("startDate") && { startDate: searchParams.get("startDate") || undefined }),
    };

    console.log("Filtros construídos:", filters);

    const data = await loadProposals(filters);
    return NextResponse.json(data);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error: "Erro ao carregar propostas" },
      { status: 500 }
    );
  }
}
