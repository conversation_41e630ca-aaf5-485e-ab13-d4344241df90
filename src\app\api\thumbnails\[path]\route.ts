"use server";
import { prisma } from "@/src/lib/prisma";
import { storageProvider } from "@/src/lib/storage";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string } }
) {
  try {
    const fileId = decodeURIComponent(params.path);
    console.log(`Rota de API de miniaturas: Tentando obter miniatura para arquivo com ID: ${fileId}`);

    // Buscar o arquivo no banco de dados primeiro para obter o caminho da miniatura
    const fileRecord = await prisma.file.findUnique({
      where: { id: fileId },
    });

    if (!fileRecord) {
      console.log(`Registro de arquivo não encontrado no banco de dados: ${fileId}`);
      return NextResponse.json({ error: "File record not found." }, { status: 404 });
    }

    // Verificar se o arquivo tem uma miniatura
    if (!fileRecord.thumbnailPath) {
      console.log(`Arquivo não possui miniatura: ${fileId}`);
      
      // Se não tiver miniatura, redirecionar para a API de arquivos normal
      return NextResponse.redirect(new URL(`/api/files/${fileId}`, request.url));
    }

    console.log(`Miniatura encontrada no banco de dados: ${fileRecord.thumbnailPath}`);

    // Buscar a miniatura no storage
    const thumbnail = await storageProvider.get(fileRecord.thumbnailPath);
    if (!thumbnail || !thumbnail.stream) {
      console.log(`Miniatura não encontrada no storage: ${fileRecord.thumbnailPath}`);
      
      // Se a miniatura não for encontrada, redirecionar para a API de arquivos normal
      return NextResponse.redirect(new URL(`/api/files/${fileId}`, request.url));
    }

    console.log(`Retornando miniatura: ${fileRecord.thumbnailPath}`);
    return new Response(thumbnail.stream, {
      headers: {
        "Content-Type": thumbnail.contentType || "image/jpeg",
        "Content-Disposition": "inline",
        "Cache-Control": "public, max-age=31536000", // Cache por 1 ano
      },
    });
  } catch (error) {
    console.error("Error fetching thumbnail:", error);

    // Fornecer mensagem de erro mais detalhada
    let errorMessage = "Failed to fetch the thumbnail.";
    if (error instanceof Error) {
      errorMessage = `Error: ${error.message}`;
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
