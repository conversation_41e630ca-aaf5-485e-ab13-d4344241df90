"use client";

import { loadProposals } from "@/src/actions/proposals";
import { Button } from "@/src/components/ui/button";
import { TableGrid } from "@/src/components/ui/table-grid";
import { useToast } from "@/src/hooks/use-toast";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalFilters } from "@/src/types/core/proposal";
import { Eraser } from "lucide-react";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from "react";

interface ConstructionInspectionTableProps {
  columns: any[];
  onViewInspectionsClick: (proposalId: string) => void; // Usado apenas na interface, implementado no componente pai
  onPageChange?: (page: number) => void;
  customers: Customer[]; // Usado apenas na interface, para tipagem
}

export type ConstructionInspectionTableRef = {
  refresh: (page?: number, filters?: ProposalFilters) => void;
};

const ConstructionInspectionTable = forwardRef<ConstructionInspectionTableRef, ConstructionInspectionTableProps>(
  function ConstructionInspectionTable(props, ref) {
    // Extrair apenas as props que são realmente utilizadas
    const { columns, onPageChange } = props;
    const [data, setData] = useState<Proposal[]>([]);
    const [loading, setLoading] = useState(false);
    const [search, setSearch] = useState("");
    const [situationFilter, setSituationFilter] = useState<string | null>(null);
    const [customerFilter, setCustomerFilter] = useState<string | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const [pagination, setPagination] = useState({
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    });

    // Estado para rastrear filtros ativos
    const [activeFilters, setActiveFilters] = useState<{ [key: string]: boolean }>({});

    const fetchProposals = useCallback(async (
      page = 1,
      pageSize = 10,
      searchTerm = "",
      situationValue: string | null = null,
      customerValue: string | null = null
    ) => {
      try {
        setLoading(true);

        const filters: ProposalFilters = {
          page,
          pageSize,
          search: searchTerm,
          situation: [
            "PROJECT_FINISHED",
            "PROJECT_IN_PROGRESS"
          ] as any,
          // Filtrar apenas por contratos do tipo fiscalização e gerenciamento
          serviceType: ["FISCALIZACAO", "GERENCIAMENTO"] as any,
        };

        // Adicionar filtro de cliente se fornecido
        if (customerValue) {
          filters.customerId = customerValue;
        }

        // Adicionar filtro de situação específica se fornecido
        if (situationValue) {
          filters.situation = [situationValue] as any;
        }

        const result = await loadProposals(filters);

        if (result) {
          // Garantir que temos um array de propostas
          const proposalArray = Array.isArray(result) ? result : (result.data || []);
          setData(proposalArray);

          // Calcular a paginação com base no resultado
          setPagination(prev => ({
            ...prev,
            page,
            pageSize,
            total: proposalArray.length,
            totalPages: Math.ceil(proposalArray.length / pageSize)
          }));
        }
      } catch (error) {
        console.error('Fetch error:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar propostas",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }, [toast]);

    useImperativeHandle(ref, () => ({
      refresh: (page?: number, filters?: ProposalFilters) => {
        const currentPage = page || pagination.page;
        const searchTerm = filters?.search || search;
        const situationValue = filters?.situation ? filters.situation[0] as string : situationFilter;
        const customerValue = filters?.customerId || customerFilter;

        // Atualizar os estados locais
        if (searchTerm !== search) {
          setSearch(searchTerm);
        }

        if (situationValue !== situationFilter) {
          setSituationFilter(situationValue);
          setActiveFilters(prev => ({
            ...prev,
            situation: !!situationValue
          }));
        }

        if (customerValue !== customerFilter) {
          setCustomerFilter(customerValue);
          setActiveFilters(prev => ({
            ...prev,
            customer: !!customerValue
          }));
        }

        // Buscar propostas com os novos valores
        fetchProposals(
          currentPage,
          pagination.pageSize,
          searchTerm,
          situationValue,
          customerValue
        );
      }
    }), [fetchProposals, pagination.page, pagination.pageSize, search, situationFilter, customerFilter]);

    // Carregar dados quando o componente for montado
    useEffect(() => {
      fetchProposals();
    }, [fetchProposals]);

    const handleSearch = (value: string) => {
      setSearch(value);
      fetchProposals(1, pagination.pageSize, value, situationFilter, customerFilter);
    };

    // Função para lidar com filtros de colunas
    const handleColumnFilter = (columnKey: string, value: any) => {
      if (columnKey === 'situation') {
        // Atualizar o filtro de situação
        setSituationFilter(value);
        setActiveFilters(prev => ({
          ...prev,
          situation: !!value
        }));

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, value, customerFilter);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      } else if (columnKey === 'customer') {
        // Atualizar o filtro de cliente
        setCustomerFilter(value);
        setActiveFilters(prev => ({
          ...prev,
          customer: !!value
        }));

        // Buscar dados com o novo filtro
        fetchProposals(1, pagination.pageSize, search, situationFilter, value);

        // Atualizar a página atual no componente pai
        if (onPageChange) onPageChange(1);
      }
    };

    const handleClearFilters = () => {
      // Limpar o estado de pesquisa e filtros
      setSearch("");
      setSituationFilter(null);
      setCustomerFilter(null);
      setActiveFilters({});

      // Resetar para a primeira página e buscar dados
      fetchProposals(1, pagination.pageSize, "", null, null);

      // Atualizar a página atual no componente pai
      if (onPageChange) onPageChange(1);

      // Focar no campo de pesquisa após limpar
      setTimeout(() => {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
      }, 0);
    };

    return (
      <TableGrid
        columns={columns}
        data={data}
        loading={loading}
        pagination={pagination}
        onPaginationChange={(newPage, newPageSize) => {
          fetchProposals(newPage, newPageSize, search, situationFilter, customerFilter);
          if (onPageChange) onPageChange(newPage);
        }}
        onSearch={handleSearch}
        searchValue={search}
        searchInputRef={searchInputRef}
        onColumnFilter={handleColumnFilter}
        activeFilters={activeFilters}
        buttonsTemplate={
          <>
            <Button
              className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
              onClick={handleClearFilters}
            >
              Limpar filtros <Eraser className="ml-2 h-4 w-4" />
            </Button>
          </>
        }
      />
    );
  }
);

export default ConstructionInspectionTable;



