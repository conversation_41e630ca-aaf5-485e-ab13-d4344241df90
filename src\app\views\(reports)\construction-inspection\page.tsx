"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations } from "@/src/constants";
import { cn, formatCurrency, formatDate } from "@/src/lib/utils";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import { FileSearch } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import ConstructionInspectionTable, { ConstructionInspectionTableRef } from "./_components/construction-inspection-table";

export default function ConstructionInspection() {
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [isNavigating, setIsNavigating] = useState(false);
    const tableRef = useRef<ConstructionInspectionTableRef>(null);
    const router = useRouter();

    const fetchCustomers = async () => {
        try {
            const res = await fetch("/api/customers?pageSize=1000");
            if (!res.ok) throw new Error("Erro ao carregar clientes");

            const result = await res.json();
            console.log("Dados de clientes recebidos:", result);

            // Verificar se os dados estão na propriedade 'data' ou diretamente no resultado
            const customers = result.data || result.items || result;

            // Garantir que customers é um array
            if (!Array.isArray(customers)) {
                console.error("Dados de clientes não são um array:", customers);
                return [];
            }

            setCustomers(customers as Customer[]);
            return customers as Customer[];
        } catch (error) {
            console.error("Erro ao carregar clientes:", error);
            return [];
        }
    };

    useEffect(() => {
        fetchCustomers();
    }, []);

    const handleNavigation = (url: string) => {
        setIsNavigating(true);
        router.push(url);
    };

    const handleViewInspections = (proposalId: string) => {
        handleNavigation(`/views/construction-inspection/${proposalId}`);
    };



    const columns: Column<Proposal>[] = [
        {
            key: "customer",
            header: "Cliente",
            cell: (row) => (row.customer as Customer)?.name,
            sortable: true,
            filterable: true,
            filterOptions: Array.isArray(customers) ? customers.map((c) => ({
                label: c.name,
                value: c.id,
            })) : [],
            getFilterValue: (row) => (row.customer as Customer).id,
            filterType: 'server',
        },
        {
            key: "name",
            header: "Projeto",
            sortable: true,
        },
        {
            key: "budget",
            header: "Orçamento",
            cell: (row) => formatCurrency(row.budget),
            sortable: true,
        },
        {
            key: "startDate",
            header: "Data início",
            cell: (row) => formatDate(row.startDate, "DATE"),
            sortable: true,
        },
        {
            key: "endDate",
            header: "Data fim prevista",
            cell: (row) => formatDate(row.endDate, "DATE"),
            sortable: true,
        },
        {
            key: "situation",
            header: "Situação",
            cell: (row) => {
                const rowValue = row.situation as ProposalSituation;
                let badgeClass = "";

                switch (rowValue) {
                    case "NEW":
                        badgeClass = "bg-blue-100 text-blue-500";
                        break;
                    case "UNDER_ANALYSIS":
                        badgeClass = "bg-purple-100 text-purple-500";
                        break;
                    case "PROPOSAL_SENT":
                        badgeClass = "bg-indigo-100 text-indigo-500";
                        break;
                    case "PROPOSAL_ACCEPTED":
                        badgeClass = "bg-green-100 text-green-500";
                        break;
                    case "SIGN_REQUESTED":
                        badgeClass = "bg-orange-100 text-orange-500";
                        break;
                    case "SIGNED":
                        badgeClass = "bg-emerald-100 text-emerald-500";
                        break;
                    case "PROJECT_IN_PROGRESS":
                        badgeClass = "bg-amber-100 text-amber-600";
                        break;
                    case "PROJECT_FINISHED":
                        badgeClass = "bg-teal-100 text-teal-600";
                        break;
                    case "LOST":
                        badgeClass = "bg-red-100 text-red-500";
                        break;
                    default:
                        badgeClass = "bg-gray-100 text-gray-500";
                }

                return (
                    <span
                        className={cn(
                            "p-1 rounded-md font-medium text-xs",
                            badgeClass
                        )}
                    >
                        {
                            proposalSituations.find(
                                (situation) => situation.value == rowValue
                            )?.label
                        }
                    </span>
                );
            },
            sortable: true,
            filterable: true,
            filterOptions: proposalSituations
                .filter(s => ['PROJECT_FINISHED', 'PROJECT_IN_PROGRESS'].includes(s.value))
                .map((s) => ({
                    label: s.label,
                    value: s.value,
                })),
            getFilterValue: (row) => row.situation as string,
            filterType: 'server',
        },
        {
            key: "actions",
            header: "Ações",
            cell: (row) => (
                <div className="flex gap-3">
                    <FileSearch
                        className="size-5 text-yellow-500 cursor-pointer"
                        onClick={() => handleViewInspections(row.id)}
                    />
                </div>
            ),
        },
    ];

    return (
        <ContentWrapper title="Fiscalização / Gerenciamento de Obra" loading={isNavigating}>
            <ConstructionInspectionTable
                ref={tableRef}
                columns={columns}
                customers={customers}
                onViewInspectionsClick={handleViewInspections}
                onPageChange={() => { }}
            />
        </ContentWrapper>
    );
}
