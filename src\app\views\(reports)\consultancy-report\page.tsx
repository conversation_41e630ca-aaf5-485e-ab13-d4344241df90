"use client";
import ContentWrapper from "@/src/components/content-wrapper";
import { Customer } from "@/src/types/core/customer";
import { FileText } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { loadCustomers } from "@/src/actions/customers";
import { cn, formatDate } from "@/src/lib/utils";
import { proposalSituations } from "@/src/constants";
import ConsultancyReportTable, { ConsultancyReportTableRef } from "./components/consultancy-report-table";
import DownloadReportDialog from "@/src/components/download-report-dialog";
import { generateInspectionReport } from "@/src/actions/inspection-parameters";

export default function ConsultancyReport() {

    const [customers, setCustomers] = useState<Customer[]>([]);
    const [openUploadDialog, setOpenUploadDialog] = useState(false);
    const [proposalId, setProposalId] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const tableRef = useRef<ConsultancyReportTableRef>(null);

    const fetchCustomers = async () => {
        try {
            const data = await loadCustomers();
            if (data) {
                setCustomers(data.map(customer => ({
                    ...customer,
                    documentType: customer.documentType || 'CPF' // Provide a default value if null
                })) as Customer[]);
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        fetchCustomers();
    }, []);

    const handleViewReport = (id: string) => {
        setOpenUploadDialog(true);
        setProposalId(id);
    };


    const columns = [
        {
            key: "customer",
            header: "Cliente",
            cell: (row: any) => row.customer?.name || "N/A",
            sortable: true,
            filterable: true,
            filterOptions: customers.map(({ id, name }) => ({
                label: name,
                value: id,
            })),
        },
        {
            key: "name",
            header: "Titulo",
            cell: (row: any) => row.name || "N/A",
            sortable: true,
        },
        {
            key: "startDate",
            header: "Data início",
            cell: (row: any) => formatDate(row.startDate, "DATE"),
            sortable: true,
        },
        {
            key: "endDate",
            header: "Data fim prevista",
            cell: (row: any) => formatDate(row.endDate, "DATE"),
            sortable: true,
        },
        {
            key: "situation",
            header: "Situação",
            cell: (row: any) => {
                const rowValue = row.situation;
                let badgeClass = "";

                switch (rowValue) {
                    case "NEW":
                        badgeClass = "bg-blue-100 text-blue-500";
                        break;
                    case "UNDER_ANALYSIS":
                        badgeClass = "bg-purple-100 text-purple-500";
                        break;
                    case "PROPOSAL_SENT":
                        badgeClass = "bg-indigo-100 text-indigo-500";
                        break;
                    case "PROPOSAL_ACCEPTED":
                        badgeClass = "bg-green-100 text-green-500";
                        break;
                    case "SIGN_REQUESTED":
                        badgeClass = "bg-orange-100 text-orange-500";
                        break;
                    case "SIGNED":
                        badgeClass = "bg-emerald-100 text-emerald-500";
                        break;
                    case "PROJECT_IN_PROGRESS":
                        badgeClass = "bg-amber-100 text-amber-600";
                        break;
                    case "PROJECT_FINISHED":
                        badgeClass = "bg-teal-100 text-teal-600";
                        break;
                    case "LOST":
                        badgeClass = "bg-red-100 text-red-500";
                        break;
                    default:
                        badgeClass = "bg-gray-100 text-gray-500";
                }

                return (
                    <span
                        className={cn(
                            "p-1 rounded-md font-medium text-xs",
                            badgeClass
                        )}
                    >
                        {
                            proposalSituations.find(
                                (situation) => situation.value == rowValue
                            )?.label || rowValue
                        }
                    </span>
                );
            },
            sortable: true,
        },
        {
            key: "actions",
            header: "Ações",
            cell: (row: any) => (
                <div className="flex justify-start">
                    <button
                        className="p-2 rounded-md hover:bg-gray-100"
                        onClick={() => handleViewReport(row.id)}
                        title="Gerar Laudo"
                    >
                        <FileText className="size-5 text-yellow-500" />
                    </button>
                </div>
            ),
        },
    ];
    return (
        <ContentWrapper title="Relatório de Consultoria">
            <ConsultancyReportTable
                ref={tableRef}
                columns={columns}
                customers={customers}
                onViewClick={handleViewReport}
                onPageChange={(page) => setCurrentPage(page)}
            />
            <DownloadReportDialog
                reportMergeAction={generateInspectionReport}
                reportType="CONSULTANCY"
                additionalParamsOptional={{ proposalId }}
                openUploadDialog={openUploadDialog}
                setOpenUploadDialog={(open) => {
                    setOpenUploadDialog(open);
                    if (!open) {
                        // Recarregar a tabela mantendo a página atual quando o diálogo for fechado
                        tableRef.current?.refresh(currentPage);
                    }
                }}
            />
        </ContentWrapper>
    )
}
