"use client"
import { getInspectionReportData } from "@/src/actions/direct-inspection-report";
import { findProposal } from "@/src/actions/proposals";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import { Column } from "@/src/components/ui/table-grid";
import { TouchTooltip } from "@/src/components/ui/touch-tooltip";
import { formatDate } from "@/src/lib/utils";
import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { Proposal } from "@/src/types/core/proposal";
import { FileText, Loader2 } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import InspectionReportsTable, { InspectionReportsTableRef } from "../components/inspection-reports-table";

import DownloadReportDialog from "@/src/components/download-report-dialog";
import { generateProjectReport } from "@/src/actions/project-report";
// import PdfViewerDialog from "@/src/components/pdf-viewer-dialog";

type InspectionListPage = {
    params: {
        id: string;
    };
};

export default function InspectionsReports({ params }: InspectionListPage) {
    const [isNavigating, setIsNavigating] = useState(false);
    // const [loadingPdfId, setLoadingPdfId] = useState<string | null>(null);
    // const [pdfViewerOpen, setPdfViewerOpen] = useState(false);
    // const [pdfData, setPdfData] = useState<{ url: string; fileName: string; blob?: Blob; inspectionId?: string } | null>(null);
    // const [proposal, setProposal] = useState<Proposal | null>(null);
    const [proposalId, setProposalId] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    // const [isProjectInProgress, setIsProjectInProgress] = useState(false);
    const [openUploadDialog, setOpenUploadDialog] = useState(false);
    const tableRef = useRef<InspectionReportsTableRef>(null);
    const router = useRouter();

    const handleNavigation = (url: string) => {
        setIsNavigating(true);
        router.push(url);
    };

    // const handleOpenProjectReportDialog = () => {
    //     setOpenUploadDialog(true);
    // };

    // Buscar dados da proposta para verificar o status
    // useEffect(() => {
    //     const fetchProposalData = async () => {
    //         try {
    //             const data = await findProposal(params.id);
    //             if (data) {
    //                 setProposal(data);
    //                 // Verificar se o projeto está em andamento
    //                 setIsProjectInProgress(data.situation === "PROJECT_IN_PROGRESS");
    //             }
    //         } catch (error) {
    //             console.error("Erro ao carregar dados da proposta:", error);
    //         }
    //     };

    //     fetchProposalData();
    // }, [params.id]);

    // const handleGenerateReport = async (id: string) => {
    //     try {
    //         // Ativar indicador de loading
    //         setLoadingPdfId(id);

    //         // Gerar PDF
    //         const data = await getInspectionReportData(id);
    //         const result = await generateDirectInspectionPdf(data);

    //         if (result.success) {
    //             // Armazenar os dados do PDF para o visualizador
    //             if (result.blobUrl && result.fileName && result.blob) {
    //                 setPdfData({
    //                     url: result.blobUrl,
    //                     fileName: result.fileName,
    //                     blob: result.blob,
    //                     inspectionId: id // Armazenar o ID da inspeção para uso posterior
    //                 });
    //             } else {
    //                 console.error('Missing blobUrl, fileName or blob in result:', result);
    //                 toast({
    //                     title: "Erro",
    //                     description: "Falha ao gerar URL do relatório.",
    //                     variant: "destructive"
    //                 });
    //                 return;
    //             }

    //             // Abrir o visualizador de PDF
    //             setPdfViewerOpen(true);

    //             toast({
    //                 title: "Sucesso",
    //                 description: "Relatório gerado com sucesso!",
    //                 variant: "default",
    //                 duration: 3000 // 3 segundos em vez do padrão (geralmente 5 segundos)
    //             });
    //         } else {
    //             toast({
    //                 title: "Erro",
    //                 description: `Falha ao gerar relatório: ${result.error || 'Erro desconhecido'}`,
    //                 variant: "destructive"
    //             });
    //         }
    //     } catch (error) {
    //         console.error("Erro ao gerar relatório:", error);
    //         toast({
    //             title: "Erro",
    //             description: "Falha ao gerar relatório.",
    //             variant: "destructive"
    //         });
    //     } finally {
    //         // Desativar indicador de loading
    //         setLoadingPdfId(null);
    //     }
    // };
    const handleViewReport = (id: string) => {
        setOpenUploadDialog(true);
        setProposalId(id);
    };

    // const handleOpenTemplateDialog = (id: string) => {
    //     setInspectionId(id);
    //     setOpenUploadDialog(true);
    // };

    const columns: Column<InspectionParameter>[] = [
        {
            key: "numberInspection",
            header: "Nº Inspeção",
            sortable: true,
        },
        {
            key: "technicalData",
            header: "Dados técnicos",
            sortable: true,
            cell: (row) => {
                const technicalData = row.technicalData as string;
                const displayText = technicalData.length > 50 ? technicalData.substring(0, 50) + "..." : technicalData;

                return (
                    <TouchTooltip
                        content={technicalData}
                        maxWidth="400px"
                        className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
                    >
                        {displayText}
                    </TouchTooltip>
                );
            }
        },
        {
            key: "observation",
            header: "Observações",
            sortable: true,
            cell: (row) => {
                const observation = row.observation as string;
                const displayText = observation.length > 50 ? observation.substring(0, 50) + "..." : observation;

                return (
                    <TouchTooltip
                        content={observation}
                        maxWidth="400px"
                        className="max-w-[200px] truncate hover:text-blue-500 transition-colors"
                    >
                        {displayText}
                    </TouchTooltip>
                );
            }
        },
        {
            key: "inspectionDate",
            header: "Data da fiscalização",
            cell: (row) => formatDate(row.inspectionDate, "DATE"),
            sortable: true,
        },
        // {
        //     key: "actions",
        //     header: "Ações",
        //     cell: (row) => (
        //         <div className="flex gap-3">
        //             {loadingPdfId === row.id ? (
        //                 <Loader2 className="size-5 text-yellow-500 animate-spin" />
        //             ) : (
        //                 <FileText
        //                     className="size-5 text-yellow-500 cursor-pointer"
        //                     onClick={() => handleGenerateReport(row.id)}
        //                 />
        //             )}
        //             {/* <FileText
        //                 className="size-5 text-blue-500 cursor-pointer"
        //                 onClick={() => handleOpenTemplateDialog(row.id)}
        //             /> */}
        //         </div>
        //     )
        // }
        {
            key: "actions",
            header: "Ações",
            cell: (row: any) => (
                <div className="flex justify-start">
                    <button
                        className="p-2 rounded-md hover:bg-gray-100"
                        onClick={() => handleViewReport(row.id)}
                        title="Gerar Relatório de Projeto"
                    >
                        <FileText className="size-5 text-yellow-500" />
                    </button>
                </div>
            ),
        },
    ];

    return (
        <ContentWrapper title="Inspeções" loading={isNavigating}>
            <InspectionReportsTable
                ref={tableRef}
                columns={columns}
                proposalId={params.id}
                // onGenerateReportClick={handleGenerateReport}
                onPageChange={(page) => setCurrentPage(page)}
                buttonsTemplate={
                    <>
                        <Button
                            className="w-full sm:w-auto bg-blue-500 hover:bg-blue-400"
                            onClick={() => tableRef.current?.refresh()}
                        >
                            Limpar filtros <Loader2 className="ml-2 h-4 w-4" />
                        </Button>
                        {/* <Button
                            className="w-full sm:w-auto bg-yellow-500 hover:bg-yellow-400"
                            onClick={handleOpenProjectReportDialog}
                        >
                            Gerar Relatório de Projeto <FileText className="ml-2 h-4 w-4" />
                        </Button> */}
                        {/* {isProjectInProgress && (
                            <Button
                                className="w-full sm:w-auto bg-green-500 hover:bg-green-400"
                                onClick={() => handleNavigation(`/views/crm/proposals/inspection-parameters?id=${params.id}`)}
                            >
                                Adicionar <Plus className="ml-2 h-4 w-4" />
                            </Button>
                        )} */}
                    </>
                }
            />
            <div className="flex justify-end mt-4">
                <Button
                    variant="outline"
                    className="border-blue-500 text-blue-500 hover:bg-blue-50"
                    onClick={() => handleNavigation("/views/construction-inspection")}
                >
                    Voltar
                </Button>
            </div>

            {/* Visualizador de PDF
            {pdfData && (
                <PdfViewerDialog
                    open={pdfViewerOpen}
                    onOpenChange={setPdfViewerOpen}
                    pdfUrl={pdfData.url}
                    fileName={pdfData.fileName}
                    pdfBlob={pdfData.blob}
                    showEmailButton={true}
                    customerEmail={proposal?.customer?.email}
                    customerName={proposal?.customer?.name}
                    proposalName={`Relatório de Fiscalização: ${proposal?.name}`}
                    inspectionId={pdfData.inspectionId}
                    reportType="inspection"
                />
            )} */}

            {/* Diálogo de Download de Relatório de Projeto */}
            <DownloadReportDialog
                reportMergeAction={generateProjectReport}
                reportType="PROJECT"
                additionalParamsOptional={{ proposalId }}
                openUploadDialog={openUploadDialog}
                setOpenUploadDialog={(open) => {
                    setOpenUploadDialog(open);
                    if (!open) {
                        // Recarregar a tabela mantendo a página atual quando o diálogo for fechado
                        tableRef.current?.refresh(currentPage);
                    }
                }}
            />
        </ContentWrapper>
    );
}
