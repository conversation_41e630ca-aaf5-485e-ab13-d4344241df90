"use client";

import {
  toggleMemberStatus,
} from "@/src/actions/membership";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import ContentWrapper from "@/src/components/content-wrapper";
import { Switch } from "@/src/components/ui/switch";
import { useOrganization } from "@/src/hooks/use-organization";
import { useToast } from "@/src/hooks/use-toast";
import { contructColumn } from "@/src/lib/table/columns";
import { formatDate } from "@/src/lib/utils";
import { useRef, useState } from "react";
import MembersTable, { MembersTableRef } from "./components/members-table";

// interface Member {
//   id: string;
//   enabled: boolean;
//   createdAt: Date;
//   user: {
//     name: string;
//     email: string;
//   };
// }

// interface PaginationState {
//   page: number;
//   pageSize: number;
//   total: number;
//   totalPages: number;
// }

export default function ManageMembers() {
  const [currentPage, setCurrentPage] = useState(1);
  const tableRef = useRef<MembersTableRef>(null);
  const { organizationId } = useOrganization();
  const { toast } = useToast();



  const handleToggleStatus = async (memberId: string) => {
    try {
      await toggleMemberStatus(memberId);
      toast({
        title: "Sucesso",
        description: "Status alterado com sucesso!",
        variant: "default"
      });
      tableRef.current?.refresh(currentPage);
    } catch (error) {
      console.error(error);
      toast({
        title: "Erro",
        description: "Erro ao alterar status do membro",
        variant: "destructive"
      });
    }
  };

  const columns = [
    contructColumn("user.name", "Nome", (row: any) => row.user?.name || "-"),
    contructColumn("user.email", "Email", (row: any) => row.user?.email || "-"),
    contructColumn("createdAt", "Data de cadastro", (row: any) =>
      formatDate(row.createdAt, "DATE")
    ),
    contructColumn("enabled", "Status", (row: any) => {
      const enabled = row.enabled;
      return (
        <AppConfirmationDialog
          title={`Deseja realmente ${enabled ? "desabilitar" : "habilitar"
            } este membro?`}
          description="Esta ação pode ser revertida posteriormente."
          onConfirmCallback={() => handleToggleStatus(row.id,)}
          dialogCancelClassName="bg-blue-500 hover:bg-blue-600"
          dialogActionClassName="bg-green-500 hover:bg-green-600"
          confirmButtonText="Confirmar"
        >
          <div className="flex items-center space-x-2">
            <Switch checked={!!enabled} />
            <span>{enabled ? "Ativo" : "Inativo"}</span>
          </div>
        </AppConfirmationDialog>
      );
    }),
  ];



  return (
    <ContentWrapper title="Gerenciar Membros">
      <MembersTable
        ref={tableRef}
        columns={columns}
        onRefreshClick={() => { }}
        onPageChange={(page) => setCurrentPage(page)}
        organizationId={organizationId}
      />
    </ContentWrapper>
  );
}
