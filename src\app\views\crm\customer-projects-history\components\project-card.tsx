"use client";

import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Proposal } from "@/src/types/core/proposal";
import { ChevronDown, ChevronUp, FileText, FileCheck, Folder, Download, Eye } from "lucide-react";
import { useState } from "react";
import { Button } from "@/src/components/ui/button";
import DocumentPreviewDialog from "@/src/components/app-document-preview";

interface ProjectCardProps {
  project: Proposal;
}

// Função para verificar se o arquivo é um PDF
const isPdfFile = (file: any) => {
  if (!file) return false;

  // Verificar pelo tipo MIME
  if (file.type && file.type.toLowerCase() === 'application/pdf') {
    return true;
  }

  // Verificar pela extensão do nome do arquivo
  if (file.name) {
    const fileName = file.name.toLowerCase();
    return fileName.endsWith('.pdf');
  }

  return false;
};

// Função para determinar o tipo de arquivo para o DocumentPreviewDialog
const getFileType = (file: any): "pdf" | "image" | "other" => {
  if (!file) return "other";

  // Verificar se é PDF
  if (isPdfFile(file)) return "pdf";

  // Verificar se é imagem
  if (file.type && file.type.startsWith('image/')) return "image";
  if (file.name) {
    const fileName = file.name.toLowerCase();
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    if (imageExtensions.some(ext => fileName.endsWith(ext))) return "image";
  }

  return "other";
};

export default function ProjectCard({ project }: ProjectCardProps) {
  const [expanded, setExpanded] = useState(false);

  // Log para depuração
  console.log('Projeto recebido:', {
    id: project.id,
    name: project.name,
    isCustomProject: project.isCustomProject,
    files: project.files ? project.files.length : 0,
    filesDetails: project.files ? project.files.map(f => ({ id: f.id, name: f.name })) : [],
    file: project.file ? {
      id: project.file.id,
      name: project.file.name
    } : null,
    contract: project.contract ? {
      id: project.contract.id,
      fileEditorId: project.contract.fileEditorId,
      fileId: project.contract.fileId,
      file: project.contract.file ? {
        id: project.contract.file.id,
        name: project.contract.file.name
      } : null
    } : null
  });

  // Função para obter o label do status
  const getSituationLabel = (situation: string | undefined) => {
    switch (situation) {
      case "NEW":
        return "Nova";
      case "UNDER_ANALYSIS":
        return "Em análise";
      case "PROPOSAL_SENT":
        return "Proposta enviada";
      case "PROPOSAL_ACCEPTED":
        return "Proposta aceita";
      case "SIGN_REQUESTED":
        return "Assinatura solicitada";
      case "SIGNED":
        return "Assinado";
      case "PROJECT_IN_PROGRESS":
        return "Projeto em andamento";
      case "PROJECT_FINISHED":
        return "Projeto concluído";
      case "LOST":
        return "Perdido";
      default:
        return "Desconhecido";
    }
  };

  // Função para obter a classe CSS do status
  const getSituationClass = (situation: string | undefined) => {
    // Se for um projeto personalizado, usar uma classe específica
    if (project.isCustomProject) {
      return "status-badge status-badge-custom-project";
    }

    switch (situation) {
      case "NEW":
        return "status-badge status-badge-new";
      case "UNDER_ANALYSIS":
        return "status-badge status-badge-analysis";
      case "PROPOSAL_SENT":
        return "status-badge status-badge-sent";
      case "PROPOSAL_ACCEPTED":
        return "status-badge status-badge-accepted";
      case "SIGN_REQUESTED":
        return "status-badge status-badge-sign";
      case "SIGNED":
        return "status-badge status-badge-signed";
      case "PROJECT_IN_PROGRESS":
        return "status-badge status-badge-in-progress";
      case "PROJECT_FINISHED":
        return "status-badge status-badge-finished";
      case "LOST":
        return "status-badge status-badge-lost";
      default:
        return "status-badge";
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg border border-gray-100 hover:border-gray-200">
      {/* Cabeçalho do card */}
      <div
        className="p-6 cursor-pointer hover:bg-gray-50 transition-colors duration-200 relative"
        onClick={() => setExpanded(!expanded)}
      >
        <div className="flex flex-col md:flex-row justify-between gap-4">
          {/* Coluna esquerda - Nome e Status */}
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-200">
              {project.name || 'Sem título'}
            </h3>

            <div className="flex flex-wrap items-center gap-3 mt-3">
              {/* Status da proposta */}
              <span className={getSituationClass(project.situation)}>
                {project.isCustomProject ? "Projeto Anterior" : getSituationLabel(project.situation)}
              </span>

              {/* Data de criação */}
              <div className="flex items-center text-gray-500 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {(() => {
                  try {
                    if (!project.createdAt) return 'Não definida';

                    if (typeof project.createdAt === 'string') {
                      // Verificar se a string da data é válida
                      const testDate = new Date(project.createdAt);
                      return isNaN(testDate.getTime()) ? 'Não definida' : formatDate(project.createdAt);
                    } else {
                      return 'Não definida';
                    }
                  } catch (e) {
                    console.error('Erro ao formatar data de criação:', e);
                    return 'Não definida';
                  }
                })()}
              </div>
            </div>
          </div>

          {/* Coluna direita - Valor e botão de expandir */}
          <div className="flex items-center gap-4">
            {/* Valor da proposta */}
            <div className="text-right">
              <div className="text-xs text-gray-500 mb-1">{project.isCustomProject ? "Valor do contrato" : "Valor total"}</div>
              <div className="font-bold text-xl text-green-600">
                {(() => {
                  try {
                    // Verificar se o orçamento existe
                    if (!project.budget) return formatCurrency(0);

                    // Converter para número se for string
                    const budgetValue = typeof project.budget === 'number' ? project.budget : Number(project.budget);

                    // Verificar se é um número válido
                    if (isNaN(budgetValue)) return formatCurrency(0);

                    // Formatar o valor
                    return formatCurrency(budgetValue);
                  } catch (e) {
                    console.error('Erro ao formatar orçamento:', e);
                    return formatCurrency(0);
                  }
                })()}
              </div>
            </div>

            {/* Botão de expandir */}
            <div className={`p-2 rounded-full transition-colors duration-200 ${expanded ? 'bg-blue-100' : 'bg-gray-100'} hover:bg-blue-200`}>
              {expanded ? (
                <ChevronUp className={`h-5 w-5 ${expanded ? 'text-blue-600' : 'text-gray-500'}`} />
              ) : (
                <ChevronDown className={`h-5 w-5 ${expanded ? 'text-blue-600' : 'text-gray-500'}`} />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo expandido */}
      {expanded && (
        <div className="p-6 border-t border-gray-100 bg-white animate-fadeIn">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gradient-to-br from-green-50 to-white p-5 rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-sm font-medium text-green-700">Data de início</p>
              </div>
              <p className="text-lg font-semibold text-gray-800 ml-7">
                {(() => {
                  try {
                    if (!project.startDate) return 'Não definida';

                    if (typeof project.startDate === 'string') {
                      // Verificar se a string da data é válida
                      const testDate = new Date(project.startDate);
                      return isNaN(testDate.getTime()) ? 'Não definida' : formatDate(project.startDate);
                    } else {
                      return 'Não definida';
                    }
                  } catch (e) {
                    console.error('Erro ao formatar data de início:', e);
                    return 'Não definida';
                  }
                })()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-white p-5 rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-sm font-medium text-green-700">Data de término</p>
              </div>
              <p className="text-lg font-semibold text-gray-800 ml-7">
                {(() => {
                  try {
                    if (!project.endDate) return 'Não definida';

                    if (typeof project.endDate === 'string') {
                      // Verificar se a string da data é válida
                      const testDate = new Date(project.endDate);
                      return isNaN(testDate.getTime()) ? 'Não definida' : formatDate(project.endDate);
                    } else {
                      return 'Não definida';
                    }
                  } catch (e) {
                    console.error('Erro ao formatar data de término:', e);
                    return 'Não definida';
                  }
                })()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-white p-5 rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
                <p className="text-sm font-medium text-green-700">Área</p>
              </div>
              <p className="text-lg font-semibold text-gray-800 ml-7">
                {(() => {
                  try {
                    // Verificar se a área existe
                    if (project.area === null || project.area === undefined) return '0 m²';

                    // Converter para número se for string
                    const areaValue = typeof project.area === 'number' ? project.area : Number(project.area);

                    // Verificar se é um número válido
                    if (isNaN(areaValue)) return '0 m²';

                    // Formatar com 2 casas decimais
                    return `${areaValue.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} m²`;
                  } catch (e) {
                    console.error('Erro ao formatar área:', e);
                    return '0 m²';
                  }
                })()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-white p-5 rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center mb-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm font-medium text-green-700">Valor por m²</p>
              </div>
              <p className="text-lg font-semibold text-gray-800 ml-7">
                {(() => {
                  try {
                    // Verificar se o orçamento e a área existem
                    if (!project.budget || !project.area) return formatCurrency(0);

                    // Converter para números se forem strings
                    const budgetValue = typeof project.budget === 'number' ? project.budget : Number(project.budget);
                    const areaValue = typeof project.area === 'number' ? project.area : Number(project.area);

                    // Verificar se são números válidos
                    if (isNaN(budgetValue) || isNaN(areaValue) || areaValue === 0) return formatCurrency(0);

                    // Calcular e formatar o valor por m²
                    return formatCurrency(budgetValue / areaValue);
                  } catch (e) {
                    console.error('Erro ao calcular valor por m²:', e);
                    return formatCurrency(0);
                  }
                })()}
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-white p-5 rounded-xl border border-purple-100 shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex items-center mb-2">
                <FileText className="h-5 w-5 text-purple-500 mr-2" />
                <p className="text-sm font-medium text-purple-700">Tipo de Serviço</p>
              </div>
              <p className="text-lg font-semibold text-gray-800 ml-7">
                {(() => {
                  if (!project.serviceType) return 'Não informado';

                  const serviceTypeMap: Record<string, string> = {
                    'INSPECAO': 'Inspeção',
                    'FISCALIZACAO': 'Fiscalização',
                    'GERENCIAMENTO': 'Gerenciamento',
                    'CONSULTORIA': 'Consultoria',
                    'PROJETO': 'Projeto'
                  };

                  return serviceTypeMap[project.serviceType] || project.serviceType;
                })()}
              </p>
            </div>
          </div>

          {/* Arquivos */}
          <div className="mt-8">
            <h4 className="font-semibold mb-5 text-gray-800 flex items-center text-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              {project.isCustomProject ? "Documentos do Projeto Anterior" : "Documentos do Projeto Concluído"}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {project.isCustomProject ? (
                /* Arquivos de projeto personalizado */
                <>
                  {/* Verificar se há múltiplos arquivos */}
                  {project.files && Array.isArray(project.files) && project.files.length > 0 ? (
                    /* Múltiplos arquivos */
                    <div className="col-span-full">
                      <div className="flex flex-col p-5 bg-gradient-to-br from-purple-50 to-white rounded-xl border border-purple-100 shadow-sm hover:shadow-md transition-all duration-200">
                        <div className="flex items-center mb-3">
                          <div className="bg-purple-100 p-2 rounded-lg mr-3">
                            <Folder className="h-6 w-6 text-purple-600" />
                          </div>
                          <div>
                            <span className="font-semibold text-purple-800 block">Arquivos do Projeto Anterior</span>
                            <span className="text-xs text-purple-500">{project.files.length} arquivo(s)</span>
                          </div>
                        </div>

                        {/* Usar DocumentPreviewDialog para múltiplos arquivos */}
                        <DocumentPreviewDialog
                          url={project.files.map((file) => `/api/files/${file.id}`)}
                          type={project.files.map((file) => getFileType(file))}
                          title={project.files.map((file) => file.name)}
                          template={
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-3 bg-white hover:bg-purple-600 hover:text-white border-purple-200 hover:border-purple-600 transition-colors duration-200 font-medium"
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Visualizar Arquivos ({project.files.length})
                            </Button>
                          }
                        />
                      </div>
                    </div>
                  ) : project.file ? (
                    /* Arquivo único (compatibilidade) */
                    <div className="flex flex-col p-5 bg-gradient-to-br from-purple-50 to-white rounded-xl border border-purple-100 shadow-sm hover:shadow-md transition-all duration-200 hover:translate-y-[-2px]">
                      <div className="flex items-center mb-3">
                        <div className="bg-purple-100 p-2 rounded-lg mr-3">
                          <Folder className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-purple-800 block">Projeto Anterior</span>
                          <span className="text-xs text-purple-500">{project.file.name}</span>
                        </div>
                      </div>
                      {/* Verificar se é um PDF para exibir opções adequadas */}
                      {isPdfFile(project.file) ? (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-3 bg-white hover:bg-purple-600 hover:text-white border-purple-200 hover:border-purple-600 transition-colors duration-200 font-medium"
                          onClick={() => project.file?.id && window.open(`/api/files/${project.file.id}`, '_blank')}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Visualizar PDF
                        </Button>
                      ) : (
                        <div className="mt-3">
                          <p className="text-sm text-gray-600 mb-2">
                            Visualizamos apenas PDF, mas você pode fazer o download deste documento clicando abaixo.
                          </p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white hover:bg-purple-600 hover:text-white border-purple-200 hover:border-purple-600 transition-colors duration-200 font-medium"
                            onClick={() => project.file?.id && window.open(`/api/files/${project.file.id}`, '_blank')}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            Download do arquivo
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col p-5 bg-gradient-to-br from-gray-50 to-white rounded-xl border border-gray-100 shadow-sm">
                      <div className="flex items-center mb-3">
                        <div className="bg-gray-100 p-2 rounded-lg mr-3">
                          <Folder className="h-6 w-6 text-gray-400" />
                        </div>
                        <div>
                          <span className="font-semibold text-gray-600 block">Projeto Anterior</span>
                          <span className="text-xs text-gray-400">Nenhum arquivo disponível</span>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                /* Arquivos de proposta normal */
                <>
                  {/* Arquivo de proposta */}
                  <div className="flex flex-col p-5 bg-gradient-to-br from-blue-50 to-white rounded-xl border border-blue-100 shadow-sm hover:shadow-md transition-all duration-200 hover:translate-y-[-2px]">
                    <div className="flex items-center mb-3">
                      <div className="bg-blue-100 p-2 rounded-lg mr-3">
                        <FileText className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <span className="font-semibold text-blue-800 block">Proposta</span>
                        <span className="text-xs text-blue-500">Documento principal</span>
                      </div>
                    </div>
                    {/* Verificar se a proposta tem fileEditorId (novo formato) ou file (formato antigo) */}
                    {project.fileEditorId ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-3 bg-white hover:bg-blue-600 hover:text-white border-blue-200 hover:border-blue-600 transition-colors duration-200 font-medium"
                        onClick={() => {
                          // Armazenar a rota de origem na sessionStorage antes de redirecionar
                          sessionStorage.setItem('documentEditorReferrer', window.location.pathname);
                          window.open(`/document-editor/${project.fileEditorId}`, '_blank');
                        }}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Visualizar Proposta
                      </Button>
                    ) : project.file ? (
                      isPdfFile(project.file) ? (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-3 bg-white hover:bg-blue-600 hover:text-white border-blue-200 hover:border-blue-600 transition-colors duration-200 font-medium"
                          onClick={() => project.file?.id && window.open(`/api/files/${project.file.id}`, '_blank')}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Visualizar PDF
                        </Button>
                      ) : (
                        <div className="mt-3">
                          <p className="text-sm text-gray-600 mb-2">
                            Visualizamos apenas PDF, mas você pode fazer o download deste documento clicando abaixo.
                          </p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-white hover:bg-blue-600 hover:text-white border-blue-200 hover:border-blue-600 transition-colors duration-200 font-medium"
                            onClick={() => project.file?.id && window.open(`/api/files/${project.file.id}`, '_blank')}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            Download do arquivo
                          </Button>
                        </div>
                      )
                    ) : (
                      <p className="text-sm text-gray-500 mt-2 italic">Arquivo de proposta não disponível</p>
                    )}
                  </div>

                  {/* Arquivo de contrato (se existir) */}
                  {project.contract && (
                    <div className="flex flex-col p-5 bg-gradient-to-br from-green-50 to-white rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-all duration-200 hover:translate-y-[-2px]">
                      <div className="flex items-center mb-3">
                        <div className="bg-green-100 p-2 rounded-lg mr-3">
                          <FileCheck className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-green-800 block">Contrato</span>
                          <span className="text-xs text-green-500">Documento assinado</span>
                        </div>
                      </div>

                      {/* Verificar se o contrato tem fileEditorId (novo formato) */}
                      {project.contract?.fileEditorId ? (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-3 bg-white hover:bg-green-600 hover:text-white border-green-200 hover:border-green-600 transition-colors duration-200 font-medium"
                          onClick={() => {
                            // Armazenar a rota de origem na sessionStorage antes de redirecionar
                            sessionStorage.setItem('documentEditorReferrer', window.location.pathname);
                            window.open(`/document-editor/${project.contract?.fileEditorId}`, '_blank');
                          }}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Visualizar Contrato
                        </Button>
                      ) : project.contract?.file ? (
                        // Fallback para o formato antigo (file)
                        isPdfFile(project.contract.file) ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-3 bg-white hover:bg-green-600 hover:text-white border-green-200 hover:border-green-600 transition-colors duration-200 font-medium"
                            onClick={() => project.contract?.file?.id && window.open(`/api/files/${project.contract.file.id}`, '_blank')}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            Visualizar PDF
                          </Button>
                        ) : (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600 mb-2">
                              Visualizamos apenas PDF, mas você pode fazer o download deste documento clicando abaixo.
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-white hover:bg-green-600 hover:text-white border-green-200 hover:border-green-600 transition-colors duration-200 font-medium"
                              onClick={() => project.contract?.file?.id && window.open(`/api/files/${project.contract.file.id}`, '_blank')}
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download do arquivo
                            </Button>
                          </div>
                        )
                      ) : (
                        <p className="text-sm text-gray-500 mt-2 italic">Arquivo de contrato não disponível</p>
                      )}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
