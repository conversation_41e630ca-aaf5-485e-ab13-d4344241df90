"use client";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  loadContacts,
  removeContact,
  saveContact,
} from "@/src/actions/contacts";
import { findCustomer, saveCustomer } from "@/src/actions/customers";
import {
  loadProjects,
  removeProject,
  saveProject,
} from "@/src/actions/projects";
import DocumentPreviewDialog from "@/components/app-document-preview";
import ItemCard from "@/components/app-item-card";
import { AppSteps, Step, StepContainer } from "@/components/app-steps";
import ContentWrapper from "@/components/content-wrapper";
import { Button } from "@/components/ui/button";
import { useToast } from "@/src/hooks/use-toast";
import { formatDate } from "@/src/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, Plus, CalendarRange, <PERSON>r, Banknote, FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import CustomerContactForm from "../_components/customer-contact-form";
import CustomerForm from "../_components/customer-form";
import CustomerProjectForm from "../_components/customer-project-form";
import { contactSchema, ContactSchema } from "../_schemas/contact.schema";
import { customerSchema, CustomerSchema } from "../_schemas/customer.schema";
import { projectSchema, ProjectSchema } from "../_schemas/project.schema";

type CustomerFormPage = {
  params: {
    id: string;
  };
};

export default function CustomerFormPage({ params }: CustomerFormPage) {
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState<any[]>([]);
  const [contacts, setContacts] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const [isProjectSaving, setIsProjectSaving] = useState(false);
  const [editingProject, setEditingProject] = useState<any>(null);
  const [isEditProjectModalOpen, setIsEditProjectModalOpen] = useState(false);

  const customerFormMethods = useForm<CustomerSchema>({
    resolver: zodResolver(customerSchema),
    shouldFocusError: false,

    defaultValues: async () => {
      let formData: CustomerSchema = {
        name: "",
        email: "",
        document: "",
        phone: "",
        observation: "",
        documentType: "CPF",
      };

      if (!params.id || params.id == "add") {
        setLoading(false);
        return formData;
      } else {
        try {
          const customer = await findCustomer(params.id);
          if (customer) {
            const { id, name, email, document, phone, observation, documentType } = customer;
            formData = {
              id,
              name,
              email,
              document,
              phone,
              observation: observation || "",
              documentType: documentType as "CPF" | "CNPJ" | "RG" | "CNH",
            };
          }
        } catch (error) {
          console.error(error);
        } finally {
          setLoading(false);
          return formData;
        }
      }
    },
  });

  const projectFormMethods = useForm<ProjectSchema>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      project: "",
      startDate: undefined,
      endDate: undefined,
      area: 0,
      contractValue: 0,
      serviceType: "",
      file: undefined,
      files: undefined,
    },
  });

  const contactFormMethods = useForm<ContactSchema>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      type: "EMAIL",
      date: undefined,
    },
  });

  const handleProjectDialogOpen = (open: boolean) => {
    // Se estiver abrindo o modal, limpar completamente o formulário primeiro
    if (open) {
      projectFormMethods.reset({
        project: "",
        startDate: undefined,
        endDate: undefined,
        area: 0,
        contractValue: 0,
        serviceType: "",
        file: undefined,
        files: undefined,
      });
      // Pequeno atraso para garantir que o reset seja concluído antes de abrir o modal
      setTimeout(() => setOpen(open), 50);
    } else {
      // Se estiver fechando, apenas fechar
      setOpen(open);
    }
  };

  const handleEditProjectModalClose = (open: boolean) => {
    if (!open) {
      // Limpar o projeto em edição ao fechar o modal
      setEditingProject(null);
      // Resetar o formulário
      projectFormMethods.reset({
        project: "",
        startDate: undefined,
        endDate: undefined,
        area: 0,
        contractValue: 0,
        serviceType: "",
        file: undefined,
        files: undefined,
      });
    }
    setIsEditProjectModalOpen(open);
  };

  const saveCustomerData = async (customer: CustomerSchema) => {
    try {
      const result = await saveCustomer(customer);

      if ('error' in result && result.error) {
        toast({
          title: "Erro",
          description: result.message || "Erro ao salvar cliente",
          variant: "destructive"
        });
        return null;
      }

      if (result && "id" in result) {
        customerFormMethods.setValue("id", result.id);

        toast({
          title: "Sucesso",
          description: customer.id
            ? "Cliente atualizado com sucesso!"
            : "Cliente criado com sucesso!",
          variant: "default"
        });

        return result;
      }

      return null;
    } catch (err) {
      console.error("Erro ao salvar cliente:", err);
      toast({
        title: "Erro",
        description: String(err) || "Erro ao salvar cliente",
        variant: "destructive"
      });
      return null;
    }
  };

  const createProject = ({ project, file, startDate, endDate, area, contractValue, serviceType }: ProjectSchema) => {
    const id = customerFormMethods.getValues("id");

    if (!id) {
      toast({
        title: "Erro",
        description: "ID do cliente não encontrado",
        variant: "destructive"
      });
      return;
    }

    // Verificar se o arquivo é válido
    if (!file || !(file instanceof Blob)) {
      toast({
        title: "Erro",
        description: "Arquivo inválido ou não selecionado",
        variant: "destructive"
      });
      return;
    }

    setIsProjectSaving(true);
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const fileBuffer = event.target?.result;
        if (!fileBuffer) {
          throw new Error("Falha ao ler o arquivo");
        }

        const fileData = {
          name: file instanceof File ? file.name : 'file',
          size: file.size,
          type: file.type,
          buffer: Array.from(new Uint8Array(fileBuffer as ArrayBuffer)),
        };

        const data = await saveProject({
          customerId: id,
          project,
          startDate,
          endDate,
          area: area || 0,
          contractValue: contractValue || 0,
          serviceType,
          file: fileData,
        });

        if (data) {
          toast({
            title: "Sucesso",
            description: "Projeto criado com sucesso!",
            variant: "default"
          });
          projectFormMethods.reset();
          fetchProjects();
          setOpen(false);
        }
      } catch (err) {
        console.error("Erro ao salvar projeto:", err);
        toast({
          title: "Erro",
          description: "Erro ao salvar projeto",
          variant: "destructive"
        });
      } finally {
        setIsProjectSaving(false);
      }
    };

    // Adicionar tratamento de erro para o FileReader
    reader.onerror = () => {
      console.error("Erro ao ler o arquivo");
      toast({
        title: "Erro",
        description: "Falha ao processar o arquivo",
        variant: "destructive"
      });
      setIsProjectSaving(false);
    };

    try {
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error("Erro ao ler o arquivo como ArrayBuffer:", error);
      toast({
        title: "Erro",
        description: "Falha ao processar o arquivo",
        variant: "destructive"
      });
      setIsProjectSaving(false);
    }
  };

  const fetchProjects = async () => {
    const id = customerFormMethods.getValues("id");

    if (id) {
      const data = await loadProjects(id);

      if (data) {
        console.log('Projetos carregados:', JSON.stringify(data));
        // Log para verificar se o campo serviceType está presente
        data.forEach((project: any) => {
          console.log(`Projeto ${project.id} - serviceType:`, project.serviceType);
        });
        setProjects(data);
      }
    }
  };

  const deleteProject = async (id: string) => {
    const data = await removeProject(id);

    if (data) {
      setProjects(projects.filter((project) => project.id !== id));
      toast({
        title: "Sucesso",
        description: "Projeto removido com sucesso!",
        variant: "default"
      });
    }
  };

  const openEditProjectModal = (project: any) => {
    console.log('Abrindo modal de edição para projeto:', project);

    // Primeiro, limpar completamente o formulário
    projectFormMethods.reset({
      project: "",
      startDate: "" as any,
      endDate: "" as any,
      area: 0,
      contractValue: 0,
      serviceType: "",
      file: undefined,
      files: undefined,
    });

    // Pequeno atraso para garantir que o reset seja concluído antes de carregar os novos dados
    setTimeout(() => {
      setEditingProject(project);

      // Preparar os arquivos existentes
      const existingFiles = project.files || [];
      console.log('Arquivos existentes do projeto:', existingFiles);

      projectFormMethods.reset({
        id: project.id,
        project: project.project,
        startDate: project.startDate ? new Date(project.startDate) : undefined,
        endDate: project.endDate ? new Date(project.endDate) : undefined,
        area: project.area || 0,
        contractValue: project.contractValue || 0,
        serviceType: project.serviceType || "",
        files: existingFiles, // Usar o array de arquivos
        file: existingFiles.length > 0 ? existingFiles[0] : undefined, // Manter compatibilidade
      });
      setIsEditProjectModalOpen(true);
    }, 50);
  };

  const updateProject = async (data: ProjectSchema) => {
    try {
      setIsProjectSaving(true);
      console.log('Dados do formulário para atualização:', data);

      // Usar diretamente os arquivos do formulário que já foram combinados
      const allFiles = data.files || [];
      console.log('Arquivos do formulário para salvar:', allFiles);

      // Verificar se há novos arquivos (File objects) que precisam ser processados
      const newFiles = allFiles.filter((file: any) => file instanceof File);
      const existingFiles = allFiles.filter((file: any) => !(file instanceof File));

      console.log('Novos arquivos:', newFiles);
      console.log('Arquivos existentes:', existingFiles);

      // Se há novos arquivos, processar eles
      if (newFiles.length > 0) {
        // Processar todos os novos arquivos
        const processedFiles: any[] = [];

        for (const newFile of newFiles) {
          const fileBuffer = await new Promise<ArrayBuffer>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              if (event.target?.result) {
                resolve(event.target.result as ArrayBuffer);
              } else {
                reject(new Error("Falha ao ler o arquivo"));
              }
            };
            reader.onerror = () => reject(new Error("Erro ao ler o arquivo"));
            reader.readAsArrayBuffer(newFile);
          });

          const fileData = {
            name: newFile.name,
            size: newFile.size,
            type: newFile.type,
            buffer: Array.from(new Uint8Array(fileBuffer)),
          };

          processedFiles.push(fileData);
        }

        // Combinar arquivos existentes com novos processados
        const finalFiles = [...existingFiles, ...processedFiles];
        console.log('Todos os arquivos finais para salvar:', finalFiles);

        // Atualizar o projeto com todos os arquivos
        const updatedProject = await saveProject({
          id: editingProject.id,
          customerId: customerFormMethods.getValues("id"),
          project: data.project,
          startDate: data.startDate,
          endDate: data.endDate,
          area: data.area || 0,
          contractValue: data.contractValue || 0,
          serviceType: data.serviceType,
          files: finalFiles,
        });

        if (updatedProject) {
          toast({
            title: "Sucesso",
            description: "Projeto atualizado com sucesso!",
            variant: "default"
          });
          fetchProjects();
          setIsEditProjectModalOpen(false);
        }
      } else {
        // Apenas arquivos existentes, salvar diretamente
        console.log('Salvando apenas arquivos existentes');

        const updatedProject = await saveProject({
          id: editingProject.id,
          customerId: customerFormMethods.getValues("id"),
          project: data.project,
          startDate: data.startDate,
          endDate: data.endDate,
          area: data.area || 0,
          contractValue: data.contractValue || 0,
          serviceType: data.serviceType,
          files: existingFiles,
        });

        if (updatedProject) {
          toast({
            title: "Sucesso",
            description: "Projeto atualizado com sucesso!",
            variant: "default"
          });
          fetchProjects();
          setIsEditProjectModalOpen(false);
        }
      }

      setIsProjectSaving(false);
    } catch (err) {
      console.error("Erro ao atualizar projeto:", err);
      toast({
        title: "Erro",
        description: "Erro ao atualizar projeto",
        variant: "destructive"
      });
      setIsProjectSaving(false);
    }
  };

  const createContact = async ({ name, type, date }: ContactSchema) => {
    const id = customerFormMethods.getValues("id");
    if (!id) {
      toast({
        title: "Erro",
        description: "ID do cliente não encontrado",
        variant: "destructive"
      });
      return;
    }

    const contact: ContactSchema = { customerId: id, name, type, date };

    const data = await saveContact(contact);

    if (data) {
      contactFormMethods.reset();
      fetchContacts();
      toast({
        title: "Sucesso",
        description: "Contato salvo com sucesso!",
        variant: "default"
      });
      return data;
    } else {
      toast({
        title: "Erro",
        description: "Erro ao salvar contato",
        variant: "destructive"
      });
    }
  };

  const fetchContacts = async () => {
    const id = customerFormMethods.getValues("id");

    if (id) {
      const data = await loadContacts(id);
      if (data) setContacts(data);
    }
  };

  const deleteContact = async (id: string) => {
    const data = await removeContact(id);

    if (data) {
      setContacts(contacts.filter((contact) => contact.id !== id));
      toast({
        title: "Sucesso",
        description: "Contato removido com sucesso!",
        variant: "default"
      });
    }
  };

  const personalInfoTemplate = (
    <StepContainer>
      <CustomerForm methods={customerFormMethods} />
    </StepContainer>
  );

  const projectHistoryTemplate = (
    <StepContainer>
      <div className="flex flex-col gap-6">
        <h1 className="text-2xl font-bold text-green-500">
          Histórico de Projetos
        </h1>

        <Dialog open={open} onOpenChange={handleProjectDialogOpen}>
          <DialogTrigger asChild>
            <button
              type="button"
              className="w-full p-6 hover:bg-green-50 border-dashed border-2 border-green-300 text-green-600 flex flex-col items-center justify-center rounded-lg transition-colors duration-300 hover:shadow-sm"
              onClick={() => handleProjectDialogOpen(true)}
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Plus className="size-8 text-green-600" />
              </div>
              <p className="font-bold text-lg">
                Anexar documentos ao histórico
              </p>
              <p className="text-sm text-green-500 mt-1">
                Clique para registrar um novo projeto
              </p>
            </button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Adicionar documento ao seu historico</DialogTitle>
              <DialogDescription />
            </DialogHeader>
            <CustomerProjectForm
              methods={projectFormMethods}
              onFileInputChange={(files) =>
                // files pode ser File[] ou null, pega o primeiro arquivo ou undefined
                projectFormMethods.setValue("file", files && files.length > 0 ? files[0] : undefined)
              }
            />

            <DialogFooter className="flex flex-col gap-3">
              <Button
                type="button"
                variant="outline"
                className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
                disabled={isProjectSaving}
                onClick={() => handleProjectDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                type="button"
                className="bg-green-500 text-white"
                onClick={() => projectFormMethods.handleSubmit(createProject)()}
                disabled={isProjectSaving}
              >
                {isProjectSaving ? "Salvando..." : "Salvar"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Modal de Edição de Projeto */}
      <Dialog open={isEditProjectModalOpen} onOpenChange={handleEditProjectModalClose}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Editar Projeto</DialogTitle>
            <DialogDescription />
          </DialogHeader>
          <CustomerProjectForm
            methods={projectFormMethods}
            onFileInputChange={(files) => {
              console.log('Novos arquivos selecionados:', files);

              // Obter arquivos existentes do projeto
              const existingFiles = editingProject?.files || [];
              console.log('Arquivos existentes:', existingFiles);

              // Se há novos arquivos, combinar com os existentes
              if (files && files.length > 0) {
                // Combinar arquivos existentes com novos arquivos
                const allFiles = [...existingFiles, ...files];
                console.log('Todos os arquivos combinados:', allFiles);

                // Atualizar ambos os campos
                projectFormMethods.setValue("files", allFiles);
                projectFormMethods.setValue("file", files[0]); // Primeiro novo arquivo para compatibilidade
              } else {
                // Se não há novos arquivos, manter apenas os existentes
                projectFormMethods.setValue("files", existingFiles);
                projectFormMethods.setValue("file", undefined);
              }
            }}
          />

          <DialogFooter className="flex flex-col gap-3">
            <Button
              type="button"
              variant="outline"
              className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
              disabled={isProjectSaving}
              onClick={() => handleEditProjectModalClose(false)}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              className="bg-green-500 text-white"
              onClick={() => projectFormMethods.handleSubmit(updateProject)()}
              disabled={isProjectSaving}
            >
              {isProjectSaving ? "Salvando..." : "Salvar Alterações"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mt-8">
        {!!projects.length &&
          projects.map((project) => (
            console.log(projects),
            <ItemCard
              key={project.id}
              name={project.project}
              description={project.project}
              onEdit={() => openEditProjectModal(project)}
              onDelete={() => deleteProject(project.id)}
              onViewTemplate={
                project?.files && project.files.length > 0 ? (
                  <DocumentPreviewDialog
                    template={
                      <Button variant="outline" size="icon" className="rounded-full hover:bg-blue-50 hover:border-blue-200 transition-colors duration-200">
                        <Eye className="size-5 text-blue-500 cursor-pointer" />
                      </Button>
                    }
                    url={
                      project.files.length === 1
                        ? `/api/files/${project.files[0].id}`
                        : project.files.map((f: any) => `/api/files/${f.id}`)
                    }
                    type={
                      project.files.length === 1
                        ? (
                          project.files[0].type?.includes("pdf")
                            ? "pdf"
                            : project.files[0].type?.includes("image")
                              ? "image"
                              : "other"
                        )
                        : project.files.map((f: any) =>
                          f.type?.includes("pdf")
                            ? "pdf"
                            : f.type?.includes("image")
                              ? "image"
                              : "other"
                        )
                    }
                    title={
                      project.files.length === 1
                        ? project.files[0].name || 'Documento'
                        : project.files.map((f: any, idx: number) => f.name || `Documento ${idx + 1}`)
                    }
                  />
                ) : (
                  <Button variant="outline" size="icon" disabled className="rounded-full border-gray-200">
                    <Eye className="size-5 text-gray-400 cursor-not-allowed" />
                  </Button>
                )
              }
            >
              <div className="flex flex-col gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <div className="bg-blue-100 p-1.5 rounded-full">
                    <CalendarRange className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Período</p>
                    <p className="font-medium">
                      {(() => {
                        try {
                          const start = project.startDate ? formatDate(new Date(project.startDate)) : 'Não informada';
                          const end = project.endDate ? formatDate(new Date(project.endDate)) : 'Não informada';
                          return `${start} - ${end}`;
                        } catch {
                          return 'Período não informado';
                        }
                      })()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className="bg-green-100 p-1.5 rounded-full">
                    <Ruler className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Área</p>
                    <p className="font-medium">
                      {(() => {
                        try {
                          return typeof project.area === 'number'
                            ? `${project.area.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} m²`
                            : '0,00 m²';
                        } catch {
                          return '0,00 m²';
                        }
                      })()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className="bg-amber-100 p-1.5 rounded-full">
                    <Banknote className="h-4 w-4 text-amber-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Valor do contrato</p>
                    <p className="font-medium">
                      {(() => {
                        try {
                          return typeof project.contractValue === 'number'
                            ? project.contractValue.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL', minimumFractionDigits: 2 })
                            : 'R$ 0,00';
                        } catch {
                          return 'R$ 0,00';
                        }
                      })()}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className="bg-purple-100 p-1.5 rounded-full">
                    <FileText className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Tipo de contrato</p>
                    <p className="font-medium">
                      {(() => {
                        if (!project.serviceType) return 'Não informado';

                        const serviceTypeMap = {
                          'INSPECAO': 'Inspeção',
                          'FISCALIZACAO': 'Fiscalização',
                          'GERENCIAMENTO': 'Gerenciamento',
                          'CONSULTORIA': 'Consultoria',
                          'PROJETO': 'Projeto'
                        };

                        return serviceTypeMap[project.serviceType as keyof typeof serviceTypeMap] || project.serviceType;
                      })()}
                    </p>
                  </div>
                </div>
              </div>
            </ItemCard>

          ))}
      </div>
    </StepContainer>
  );

  const contactHistoryTemplate = (
    <StepContainer>
      <div className="flex flex-col gap-6">
        <h1 className="text-2xl font-bold text-green-500">
          Histórico de contatos
        </h1>

        <CustomerContactForm
          methods={contactFormMethods}
          onSubmit={() => contactFormMethods.handleSubmit(createContact)()}
        />
      </div>
      <div className="justify-center lg:justify-start flex flex-wrap gap-3 mt-6">
        {!!contacts.length &&
          contacts.map((contact) => (
            <ItemCard
              key={contact.id}
              name={contact.name}
              description={formatDate(contact.date)}
              onDelete={() => deleteContact(contact.id)}
            >
              <p className="font-semibold text-sm my-3">
                Contato por:
                <span className="font-normal">
                  {contact.type == "EMAIL" ? " Email" : " Telefone"}
                </span>
              </p>
            </ItemCard>
          ))}
      </div>
    </StepContainer>
  );

  const steps: Step[] = [
    {
      template: personalInfoTemplate,
      onContinueCallback: (goToStep) => {
        const handleSubmit = async (customer: CustomerSchema) => {
          const savedCustomer = await saveCustomerData(customer);
          if (savedCustomer) goToStep(2);
        };
        customerFormMethods.handleSubmit(handleSubmit)();
      },
      onBackCallback: () => router.push("/views/crm/customers"),
    },
    {
      template: projectHistoryTemplate,
      onContinueCallback: (goToStep) => {
        // Avançar diretamente para a próxima etapa sem validar o formulário de projeto
        goToStep(3);
      },
      onBackCallback: (goToStep) => goToStep(1),
    },
    {
      template: contactHistoryTemplate,
      onContinueCallback: () => router.push("/views/crm/customers"),
      onBackCallback: (goToStep) => goToStep(2),
    },
  ];

  useEffect(() => {
    const customerId = customerFormMethods.getValues("id");
    if (customerId) {
      fetchProjects();
      fetchContacts();
    }
  }, [customerFormMethods.getValues("id")]);

  return (
    <ContentWrapper
      title={params.id && params.id != "add" ? "Editar cliente" : "Adicionar cliente"}
      loading={loading}
    >
      <AppSteps steps={steps} />
    </ContentWrapper>
  );
}
