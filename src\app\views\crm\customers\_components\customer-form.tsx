import { CustomInput } from "@/src/components/app-input";
import { documentMask } from "@/src/constants";
import { FormProvider, UseFormReturn, useWatch } from "react-hook-form";
import { CustomerSchema } from "../_schemas/customer.schema";

interface CustomerFormProps {
	methods: UseFormReturn<CustomerSchema>;
	title?: string;
}

export default function CustomerForm({
	methods,
	title = "Informações Pessoais",
}: CustomerFormProps) {

	const documentType = useWatch({
		control: methods.control,
		name: "documentType"
	});

	return (
		<FormProvider {...methods}>
			<form className="flex flex-col gap-4">
				<h1 className="text-2xl font-bold text-green-500">{title}</h1>
				<div className="grid grid-cols-1 gap-2">
					<div className="grid grid-cols-1 sm:grid-cols-[2fr_1fr_1fr] gap-4">
						<CustomInput label="Nome" name="name" placeholder="Nome" />
						<CustomInput
							label="Tipo de documento"
							name="documentType"
							placeholder="Selecione o tipo de documento"
							type="select"
							items={[
								{ value: "CPF", label: "CPF" },
								{ value: "CNPJ", label: "CNPJ" },
								{ value: "RG", label: "RG" },
								{ value: "CNH", label: "CNH" },
							]}
						/>
						<CustomInput
							label="Documento"
							name="document"
							placeholder={documentType || "Selecione o tipo"}
							type="mask"
							mask={documentType ? documentMask[documentType].mask : ""}
						/>
					</div>
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<CustomInput
							type="tel"
							label="Telefone"
							name="phone"
							placeholder="(99) 9 9999-9999"
						/>
						<CustomInput
							label="Email"
							name="email"
							placeholder="<EMAIL>"
						/>
					</div>
					<CustomInput
						label="Observação"
						name="observation"
						placeholder="Observação"
					/>
				</div>
			</form>
		</FormProvider>
	);
}
