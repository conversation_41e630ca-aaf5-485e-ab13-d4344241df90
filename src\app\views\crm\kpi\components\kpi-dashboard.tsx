"use client";

import { loadKpiData } from "@/src/actions/kpi";
import { useEffect, useState } from "react";
import RevenueChart from "./revenue-chart";
import SquareMeterChart from "./square-meter-chart";
import ConversionRateChart from "./conversion-rate-chart";
import ContractsCountChart from "./contracts-count-chart";
import StatusDistributionChart from "./status-distribution-chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/src/components/ui/card";
import { CircleDollarSign, PercentIcon, Ruler, SquareStack, Users } from "lucide-react";
import { DateRangePicker } from "./date-range-picker";
import { DateRange } from "react-day-picker";
import { useIsMobile } from "@src/hooks/use-mobile";

interface KpiDashboardProps {
  onDateRangeChange?: (range: DateRange | undefined) => void;
}

export default function KpiDashboard({ onDateRangeChange }: KpiDashboardProps) {
  const [kpiData, setKpiData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1), // 1º de janeiro do ano atual
    to: new Date(new Date().getFullYear(), 11, 31)  // 31 de dezembro do ano atual
  });
  const isMobile = useIsMobile();

  // Propagar a mudança de intervalo de datas para o componente pai
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    if (onDateRangeChange) {
      onDateRangeChange(range);
    }
  };

  const fetchKpiData = async () => {
    try {
      setLoading(true);
      // Garantir que as datas sejam passadas corretamente para o backend
      const data = await loadKpiData(
        dateRange?.from,
        dateRange?.to
      );
      if (data) {
        setKpiData(data);
      }
    } catch (error) {
      console.error("Erro ao carregar dados de KPI:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchKpiData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateRange]);

  // Calcular métricas resumidas
  const totalRevenue = kpiData?.reduce((sum, item) => sum + item.totalIncome, 0) || 0;
  const averageSquareMeterValue = kpiData?.length
    ? kpiData.reduce((sum, item) => sum + parseFloat(item.valuePerSquareMeter), 0) / kpiData.length
    : 0;
  const averageConversionRate = kpiData?.length
    ? kpiData.reduce((sum, item) => sum + parseFloat(item.conversionRate), 0) / kpiData.length
    : 0;
  const totalContracts = kpiData?.reduce((sum, item) => sum + item.acceptedProposals, 0) || 0;
  const totalArea = kpiData?.reduce((sum, item) => sum + item.totalArea, 0) || 0;

  return (
    <div className="space-y-6">
      {/* Seletor de intervalo de datas */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 p-5 bg-gradient-to-r from-green-50 to-white rounded-lg shadow-md border border-green-100">
        <div>
          <h3 className="text-lg font-semibold mb-1 text-green-800">Filtrar indicadores por período</h3>
          <p className="text-sm text-green-600">Selecione um intervalo de datas para visualizar os dados específicos</p>
        </div>
        <div className="w-full md:w-auto">
          <DateRangePicker
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            className="w-full md:w-auto"
          />
        </div>
      </div>

      {/* Cards de resumo */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Faturamento Total</CardTitle>
            <CircleDollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              R$ {totalRevenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              Valor total dos contratos assinados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor por m²</CardTitle>
            <Ruler className="h-4 w-4 text-indigo-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-600">
              R$ {averageSquareMeterValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </div>
            <p className="text-xs text-muted-foreground">
              Valor médio por metro quadrado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Conversão</CardTitle>
            <PercentIcon className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {averageConversionRate.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}%
            </div>
            <p className="text-xs text-muted-foreground">
              Propostas convertidas em contratos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Contratos</CardTitle>
            <SquareStack className="h-4 w-4 text-sky-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-sky-600">
              {totalContracts}
            </div>
            <p className="text-xs text-muted-foreground">
              Número de contratos assinados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Área Total</CardTitle>
            <Users className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {totalArea.toLocaleString('pt-BR')} m²
            </div>
            <p className="text-xs text-muted-foreground">
              Área total dos projetos
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Gráficos */}
      {loading ? (
        <div className="flex items-center justify-center h-60 sm:h-96">
          <span className="text-lg">Carregando dados...</span>
        </div>
      ) : (
        <div className="flex flex-col space-y-6 sm:space-y-8">
          {/* Faturamento Mensal */}
          <div className="flex justify-center">
            <div className="w-full max-w-5xl">
              <RevenueChart data={kpiData} dateRange={dateRange} isMobile={isMobile} />
            </div>
          </div>

          {/* Valor por Metro Quadrado */}
          <div className="flex justify-center">
            <div className="w-full max-w-5xl">
              <SquareMeterChart data={kpiData} dateRange={dateRange} isMobile={isMobile} />
            </div>
          </div>

          {/* Taxa de Conversão de Propostas */}
          <div className="flex justify-center">
            <div className="w-full max-w-5xl">
              <ConversionRateChart data={kpiData} dateRange={dateRange} isMobile={isMobile} />
            </div>
          </div>

          {/* Quantidade de Contratos e Distribuição por Status */}
          <div className="flex flex-col md:flex-row justify-center gap-4 sm:gap-6">
            <div className="w-full md:w-1/2 max-w-2xl">
              <ContractsCountChart data={kpiData} dateRange={dateRange} isMobile={isMobile} />
            </div>
            <div className="w-full md:w-1/2 max-w-2xl">
              <StatusDistributionChart startDate={dateRange?.from} endDate={dateRange?.to} isMobile={isMobile} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
