import { File } from "@/src/types/core/file";
import { PlanningFrequencyItemInterface } from "@/src/types/core/proposal";
import { z } from "zod";

export const proposalsSchema = z
  .object({
    id: z.string().optional(),
    name: z
      .string()
      .refine((value) => value.trim().length, "Nome é obrigatório"),
    customerId: z
      .string()
      .refine((value) => value.trim().length, "Cliente é obrigatório"),
    proposalTemplateId: z
      .string()
      .refine((value) => value.trim().length, "Template é obrigatório"),
    startDate: z
      .date()
      .or(z.string())
      .refine((value) => value, "Data inicio é obrigatória"),
    endDate: z
      .date()
      .or(z.string())
      .refine((value) => value, "Data fim é obrigatória"),
    budget: z
      .string()
      .or(z.number())
      .refine((value) => Number(value) != 0, "Orçamento deve ser maior que 0."),
    area: z
      .string()
      .or(z.number())
      .refine((value) => Number(value) != 0, "Orçamento deve ser maior que 0."),
    cep: z
      .string()
      .optional()
      .refine((value) => {
        const cepRegex = /^\d{2}\.\d{3}-\d{3}$/;
        return value ? cepRegex.test(value) : true;
      }, "CEP inválido"),
    address: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    paymentCondition: z.union([z.literal("CASH"), z.literal("INSTALLMENTS")]),
    downPayment: z.string().optional(),
    installmentNumber: z
      .string()
      .or(z.number())
      .refine(
        (value) => value !== undefined && value !== "",
        "Número de parcelas é obrigatório quando o pagamento é parcelado"
      ),
    installmentAmount: z.string().or(z.number()).optional(),
    serviceScopes: z
      .array(z.string())
      .refine((value) => value.length > 0, "Escolha pelo menos um serviço"),
    customService: z.string().optional(),
    periodicity: z.union([
      z.literal("WEEKLY"),
      z.literal("MONTHLY"),
      z.literal("NONE"),
    ]),
    serviceType: z.string().optional(),
    situation: z.string().optional(),
    methodology: z.array(z.string()).optional(),
  })
  .refine(
    (data) => {
      // Verificar se ambas as datas estão preenchidas antes de comparar
      if (!data.startDate || !data.endDate) {
        return true; // Não validar se alguma das datas estiver vazia
      }

      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      // Verificar se as datas são válidas antes de comparar
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return true; // Não validar se alguma das datas for inválida
      }

      // Verifica se a data de início é anterior à data de fim
      return startDate < endDate;
    },
    {
      message: "A data de início deve ser anterior à data de fim",
      path: ["startDate"], // Erro associado ao campo startDate
    }
  )
  .refine(
    (data) => {
      const budget = Number(data.budget);
      const downPayment = Number(data.downPayment);

      if (!downPayment) return true;

      return downPayment < budget;
    },
    {
      message: "A entrada deve ser menor que o orçamento",
      path: ["downPayment"], // Error associated with downPayment field
    }
  )
  .refine(
    (data) => {
      const paymentCondition = data.paymentCondition;

      if (paymentCondition == "CASH") return true;

      const installmentNumber = Number(data.installmentNumber);

      return installmentNumber > 0;
    },
    {
      message: "Selecione o número de parcelas",
      path: ["installmentNumber"],
    }
  )
  // Adicionar validação condicional para o campo serviceType
  // O campo só é obrigatório quando a proposta está sendo convertida em contrato
  .superRefine((data, ctx) => {
    // Verificar se a proposta tem uma situação que indica conversão para contrato
    const contractSituations = [
      "PROPOSAL_ACCEPTED",
      "SIGN_REQUESTED",
      "SIGNED",
      "PROJECT_IN_PROGRESS",
      "PROJECT_FINISHED",
    ];

    // Se a proposta está em uma situação de contrato e o tipo de serviço não está definido
    if (
      data.situation &&
      contractSituations.includes(data.situation) &&
      (!data.serviceType || !data.serviceType.trim().length)
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Tipo de serviço é obrigatório para contratos",
        path: ["serviceType"],
      });
    }
  });

export type ProposalSchema = z.infer<typeof proposalsSchema> & {
  plannings?: PlanningFrequencyItemInterface[];
  file?: Partial<File>;
  workTotalCost?: string | number;
};
