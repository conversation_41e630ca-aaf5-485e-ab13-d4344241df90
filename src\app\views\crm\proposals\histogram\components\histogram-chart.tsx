"use client"
import { loadHistograms } from "@/src/actions/histogram";
import { generateHistogramExcel } from "@/src/actions/histogram-excel";
import { But<PERSON> } from "@/src/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip } from "@/src/components/ui/chart";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { useState, useEffect } from "react";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts";
import { generateHistogramExcel as generateExcelFile } from "@/src/lib/generate-histogram-excel";
import { downloadBlob } from "@/src/lib/excel-utils";
import { useToast } from "@/src/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useIsMobile } from "@src/hooks/use-mobile";

export default function HistogramChart() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const isMobile = useIsMobile();
    const [repairBudgetHistogram, setRepairBudgetHistogram] = useState<any[]>([]);
    const [proposalId, setProposalId] = useState<string | null>(null);
    const [loading, setLoading] = useState(true);
    const [exportLoading, setExportLoading] = useState(false);
    // Estados para filtros
    const [selectedActivity, setSelectedActivity] = useState<string>("all");
    const [showPredicted, setShowPredicted] = useState<boolean>(true);
    const [showRealized, setShowRealized] = useState<boolean>(true);
    const { toast } = useToast();

    // Função para processar nomes de serviços (preservando acentos)
    const processServiceName = (str: string) => {
        // Preservar acentos, apenas padronizar para identificação única
        return str.trim();
    };

    // Função para formatar o nome do serviço para exibição (primeira letra maiúscula)
    const formatServiceName = (str: string) => {
        if (!str) return '';
        // Dividir por espaços para capitalizar cada palavra
        return str.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Função auxiliar para converter mês/ano em data para ordenação
    const parseMonthYear = (monthYear: string): Date => {
        if (monthYear === "Sem data") return new Date(0); // Data mínima para itens sem data
        const [month, year] = monthYear.split('/');
        const monthMap: Record<string, number> = {
            'jan': 0, 'fev': 1, 'mar': 2, 'abr': 3, 'mai': 4, 'jun': 5,
            'jul': 6, 'ago': 7, 'set': 8, 'out': 9, 'nov': 10, 'dez': 11
        };
        return new Date(parseInt(year), monthMap[month.toLowerCase()] || 0, 1);
    };

    // CRITICAL FIX 3: Transformar os dados para exibir todos os meses com atividades
    console.log('CRITICAL FIX 3: Raw data from API:', repairBudgetHistogram);

    // Verificar se temos todas as medições
    console.log(`CRITICAL FIX 3: Total measurements received: ${repairBudgetHistogram.length}`);

    // Extrair todos os períodos únicos (mês/ano) das medições
    const uniqueMonthYears = new Map<string, { month: number, year: number, label: string }>();

    repairBudgetHistogram.forEach(item => {
        if (item.monthYear) {
            const { month, year, label } = item.monthYear;
            // Usar uma chave padronizada para o mês/ano (MM-YYYY)
            const key = `${month.toString().padStart(2, '0')}-${year}`;

            if (!uniqueMonthYears.has(key)) {
                uniqueMonthYears.set(key, { month, year, label });
            }
        } else if (item.periodicity && item.periodicity.label) {
            // Fallback para o label do período se não tivermos informações de mês/ano
            const label = item.periodicity.label;
            const key = label;

            if (!uniqueMonthYears.has(key)) {
                uniqueMonthYears.set(key, {
                    month: 0, // Valor padrão
                    year: 0,  // Valor padrão
                    label
                });
            }
        }
    });

    console.log('CRITICAL FIX 3: Unique month/years:', Object.fromEntries(uniqueMonthYears));

    // Extrair todos os serviços únicos
    const uniqueServices = new Map<string, { name: string, id: string }>();

    repairBudgetHistogram.forEach(item => {
        if (item.service) {
            const serviceName = processServiceName(item.service.name);

            if (!uniqueServices.has(serviceName)) {
                uniqueServices.set(serviceName, {
                    name: serviceName,
                    id: item.serviceId
                });
            }
        }
    });

    console.log('CRITICAL FIX 3: Unique services:', Object.fromEntries(uniqueServices));

    // Criar uma estrutura para armazenar os dados agrupados por período e serviço
    const groupedData = new Map<string, Map<string, { real: number, predicted: number }>>();

    // Inicializar a estrutura com todos os períodos e serviços
    uniqueMonthYears.forEach((_, monthYearKey) => {
        groupedData.set(monthYearKey, new Map());

        uniqueServices.forEach((_, serviceName) => {
            groupedData.get(monthYearKey)?.set(serviceName, { real: 0, predicted: 0 });
        });
    });

    // Preencher a estrutura com os dados das medições
    repairBudgetHistogram.forEach(item => {
        if (!item.service || (!item.monthYear && !item.periodicity)) {
            return;
        }

        // Determinar a chave do período
        let periodKey: string;

        if (item.monthYear) {
            // Usar a chave padronizada para o mês/ano
            periodKey = `${item.monthYear.month.toString().padStart(2, '0')}-${item.monthYear.year}`;
        } else {
            // Fallback para o label do período
            periodKey = item.periodicity.label;
        }

        // Obter o nome do serviço
        const serviceName = processServiceName(item.service.name);

        // Garantir que os valores sejam números
        const realValue = parseFloat(item.realPeriodPercentage || '0');
        const predictedValue = parseFloat(item.predictedPeriodPercentage || '0');

        // Verificar se temos o período e o serviço na estrutura
        const periodMap = groupedData.get(periodKey);
        if (periodMap) {
            const serviceData = periodMap.get(serviceName);
            if (serviceData) {
                // Atualizar os valores (usar o maior valor se já existir)
                serviceData.real = Math.max(serviceData.real, realValue);
                serviceData.predicted = Math.max(serviceData.predicted, predictedValue);
            } else {
                // Adicionar o serviço se não existir
                periodMap.set(serviceName, { real: realValue, predicted: predictedValue });
            }
        }
    });

    console.log('CRITICAL FIX 3: Grouped data:', Object.fromEntries(
        Array.from(groupedData.entries()).map(([key, value]) => [
            key,
            Object.fromEntries(Array.from(value.entries()))
        ])
    ));

    // Transformar a estrutura agrupada em um array para o gráfico
    const transformedData = Array.from(groupedData.entries()).map(([periodKey, serviceMap]) => {
        // Criar o objeto base com o nome do período
        const periodObj: any = {
            name: uniqueMonthYears.get(periodKey)?.label || periodKey
        };

        // Adicionar os dados de cada serviço
        serviceMap.forEach((serviceData, serviceName) => {
            periodObj[serviceName] = serviceData.real;
            periodObj[`${serviceName}Pred`] = serviceData.predicted;
        });

        return periodObj;
    });

    // Ordenar os períodos cronologicamente
    transformedData.sort((a, b) => {
        // Tentar extrair mês e ano do nome do período
        const aMonthYear = a.name.match(/(\w+)\s*\/\s*(\d{4})/);
        const bMonthYear = b.name.match(/(\w+)\s*\/\s*(\d{4})/);

        if (aMonthYear && bMonthYear) {
            // Se ambos têm formato mês/ano, comparar pelo ano e depois pelo mês
            const aYear = parseInt(aMonthYear[2], 10);
            const bYear = parseInt(bMonthYear[2], 10);

            if (aYear !== bYear) {
                return aYear - bYear;
            }

            // Comparar pelo mês
            const monthNames = [
                'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
                'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
            ];

            const aMonth = monthNames.indexOf(aMonthYear[1].toLowerCase());
            const bMonth = monthNames.indexOf(bMonthYear[1].toLowerCase());

            return aMonth - bMonth;
        }

        // Fallback para a função parseMonthYear
        return parseMonthYear(a.name).getTime() - parseMonthYear(b.name).getTime();
    });

    console.log('CRITICAL FIX 3: Transformed data for chart:', transformedData);


    // CRITICAL FIX 5: Não precisamos mais extrair chaves únicas, pois agora extraímos diretamente dos dados transformados

    // CRITICAL FIX 4: Função para gerar cores distintas para cada atividade
    const generateDistinctColors = (count: number) => {
        // Lista de cores base para atividades (cores vibrantes e distintas)
        const baseColors = [
            'rgb(22, 163, 74)',  // Verde
            'rgb(37, 99, 235)',  // Azul
            'rgb(220, 38, 38)',  // Vermelho
            'rgb(217, 119, 6)',  // Laranja
            'rgb(124, 58, 237)', // Roxo
            'rgb(6, 182, 212)',  // Ciano
            'rgb(236, 72, 153)', // Rosa
            'rgb(132, 204, 22)', // Lima
            'rgb(202, 138, 4)',  // Âmbar
            'rgb(79, 70, 229)',  // Índigo
            'rgb(168, 85, 247)', // Violeta
            'rgb(234, 88, 12)'   // Laranja escuro
        ];

        // Se tivermos mais atividades que cores, gerar cores adicionais
        if (count > baseColors.length) {
            // Gerar cores adicionais com base no HSL
            for (let i = baseColors.length; i < count; i++) {
                const hue = (i * 137.5) % 360; // Distribuir as cores uniformemente no círculo cromático
                baseColors.push(`hsl(${hue}, 70%, 50%)`);
            }
        }

        return baseColors.slice(0, count);
    };

    // CRITICAL FIX 4: Mapa para armazenar as cores de cada serviço
    const serviceColorMap = new Map<string, { main: string, light: string }>();

    // CRITICAL FIX 4: Preencher o mapa de cores
    const uniqueServiceNames = Array.from(uniqueServices.keys());
    const distinctColors = generateDistinctColors(uniqueServiceNames.length);

    uniqueServiceNames.forEach((serviceName, index) => {
        const mainColor = distinctColors[index];

        // Converter para RGB para criar uma versão mais clara
        let lightColor = mainColor;

        // Se a cor estiver no formato rgb()
        if (mainColor.startsWith('rgb')) {
            const rgbMatch = mainColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
            if (rgbMatch) {
                const r = parseInt(rgbMatch[1], 10);
                const g = parseInt(rgbMatch[2], 10);
                const b = parseInt(rgbMatch[3], 10);

                // Criar uma versão mais clara (misturar com branco)
                const lightenFactor = 0.6; // 60% mais claro
                const lightR = Math.round(r + (255 - r) * lightenFactor);
                const lightG = Math.round(g + (255 - g) * lightenFactor);
                const lightB = Math.round(b + (255 - b) * lightenFactor);

                lightColor = `rgb(${lightR}, ${lightG}, ${lightB})`;
            }
        }
        // Se a cor estiver no formato hsl()
        else if (mainColor.startsWith('hsl')) {
            const hslMatch = mainColor.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
            if (hslMatch) {
                const h = parseInt(hslMatch[1], 10);
                const s = parseInt(hslMatch[2], 10);
                const l = parseInt(hslMatch[3], 10);

                // Aumentar a luminosidade para criar uma versão mais clara
                const lightL = Math.min(l + 25, 90); // Aumentar luminosidade em 25%, máximo 90%

                lightColor = `hsl(${h}, ${s}%, ${lightL}%)`;
            }
        }

        serviceColorMap.set(serviceName, { main: mainColor, light: lightColor });
    });

    console.log('CRITICAL FIX 4: Service color map:', Object.fromEntries(serviceColorMap));

    // CRITICAL FIX 4: Função para obter a cor de um serviço
    const findColor = (serviceId: string, type: 'COLOR' | 'LIGHT_COLOR') => {
        // Remover o sufixo 'Pred' se necessário
        const cleanedServiceId = serviceId.replace(/Pred$/, '');

        // Verificar se temos uma cor para este serviço
        const colorInfo = serviceColorMap.get(cleanedServiceId);

        if (colorInfo) {
            return type === 'COLOR' ? colorInfo.main : colorInfo.light;
        }

        // Fallback para cores padrão
        return type === 'COLOR' ? 'rgb(22, 163, 74)' : 'rgb(37, 99, 235)';
    }

    // CRITICAL FIX 4: Configuração para o gráfico com cores e rótulos para valores reais e previstos
    // Agora cada atividade terá sua própria cor, então estas são apenas cores de fallback
    const chartConfig: ChartConfig = {
        real: {
            label: "Realizado",
            color: "rgb(22, 163, 74)" // Verde
        },
        predicted: {
            label: "Previsto",
            color: "rgb(146, 219, 171)" // Verde claro
        }
    };

    const fetchRepairBudgets = async (proposalId: string) => {
        try {
            setLoading(true);
            const data = await loadHistograms(proposalId);
            if (data) {
                setRepairBudgetHistogram(data);

                // CRITICAL FIX 6: Removida a média de conclusão real
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const id = searchParams.get("id")
        if (id) {
            fetchRepairBudgets(id)
            setProposalId(id)
        }
    }, [])

    // Função para gerar e baixar o relatório Excel
    const handleExportExcel = async () => {
        if (!proposalId) {
            toast({
                title: "Erro",
                description: "ID da proposta não encontrado",
                variant: "destructive",
            });
            return;
        }

        try {
            setExportLoading(true);

            // Buscar os dados para o Excel
            const data = await generateHistogramExcel(proposalId);

            if (!data) {
                throw new Error("Não foi possível gerar os dados para o Excel");
            }

            // Verificar se temos períodos com labels válidos
            console.log("Dados recebidos do servidor:", data);

            if (!data.periods || data.periods.length === 0) {
                console.warn("Nenhum período encontrado nos dados");
            } else {
                console.log("Períodos encontrados:", data.periods.map((p: any) => ({
                    id: p.id,
                    label: p.label,
                    order: p.order
                })));
            }

            // Gerar o arquivo Excel no cliente
            const excelBlob = await generateExcelFile(data);

            // Criar nome do arquivo com data atual
            const today = new Date();
            const fileName = `histograma_evolucao_${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}.xlsx`;

            // Fazer o download do arquivo
            downloadBlob(excelBlob, fileName);

            toast({
                title: "Sucesso",
                description: "Relatório exportado com sucesso!",
                variant: "default",
                duration: 3000
            });
        } catch (error) {
            console.error("Erro ao exportar relatório:", error);
            toast({
                title: "Erro",
                description: "Erro ao exportar relatório. Tente novamente.",
                variant: "destructive"
            });
        } finally {
            setExportLoading(false);
        }
    };

    return (
        <div className="flex flex-col gap-4 w-full h-full">
            <div className="mt-2 mb-4 flex flex-row justify-end gap-3">
                <Button
                    className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
                    variant="outline"
                    onClick={() => router.push("/views/crm/proposals/accepted")}
                >
                    Voltar
                </Button>
                <Button
                    className="border-blue-500 hover:bg-blue-600 bg-blue-500"
                    variant="default"
                    onClick={handleExportExcel}
                    disabled={exportLoading}
                >
                    {exportLoading ? "Gerando..." : "Gerar relatório Excel"}
                </Button>
                <Button
                    onClick={() => router.push(`/views/crm/proposals/histogram/services?id=${proposalId}`)}
                    variant="success"
                    disabled={loading}
                >
                    Realizar medição
                </Button>
            </div>

            <Card className="w-full">
                <CardHeader className="pb-2">
                    <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold`}>
                        Histograma de Evolução
                    </CardTitle>
                    <CardDescription className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                        <span className="text-xs sm:text-sm">
                            Evolução percentual dos serviços por período
                        </span>

                        {/* Filtros para atividades e tipos de valores */}
                        <div className="mt-2 sm:mt-0 flex flex-col sm:flex-row gap-2">
                            {/* Filtro de atividades */}
                            <Select
                                value={selectedActivity}
                                onValueChange={setSelectedActivity}
                            >
                                <SelectTrigger className="h-10 border-green-200 rounded-lg focus:border-green-400 focus:ring focus:ring-green-200 focus:ring-opacity-50 transition-all duration-200 bg-green-50/50 text-green-700 font-medium">
                                    <SelectValue placeholder="Filtrar por atividade" />
                                </SelectTrigger>
                                <SelectContent className="border-green-100">
                                    <SelectItem value="all" className="hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700">
                                        Todas as atividades
                                    </SelectItem>
                                    {(() => {
                                        // Extrair todos os serviços presentes nos dados transformados
                                        const servicesInData = new Set<string>();

                                        // Para cada período nos dados transformados
                                        transformedData.forEach(periodData => {
                                            // Para cada chave no período (exceto 'name')
                                            Object.keys(periodData).forEach(key => {
                                                if (key !== 'name' && !key.endsWith('Pred')) {
                                                    // Verificar se o valor é maior que zero
                                                    const value = periodData[key];
                                                    if (value > 0) {
                                                        // Adicionar o serviço ao conjunto
                                                        servicesInData.add(key);
                                                    }
                                                }
                                            });
                                        });

                                        // Renderizar as opções para cada serviço
                                        return Array.from(servicesInData).map((serviceName, index) => {
                                            // Exibir o nome do serviço formatado (primeira letra maiúscula e com acentos)
                                            const baseServiceName = serviceName.includes('_')
                                                ? serviceName.split('_')[0]
                                                : serviceName;

                                            // Formatar o nome para exibição
                                            const displayName = formatServiceName(baseServiceName);

                                            return (
                                                <SelectItem
                                                    key={index}
                                                    value={serviceName}
                                                    className="hover:bg-green-50 hover:text-green-700 focus:bg-green-50 focus:text-green-700"
                                                >
                                                    {displayName}
                                                </SelectItem>
                                            );
                                        });
                                    })()}
                                </SelectContent>
                            </Select>

                            {/* Filtros de tipo de valores */}
                            <div className="flex items-center gap-3 h-10 px-3 border border-green-200 rounded-lg bg-green-50/50 text-green-700">
                                <label className="flex items-center gap-1.5 text-sm cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={showPredicted}
                                        onChange={(e) => setShowPredicted(e.target.checked)}
                                        className="rounded border-green-300 text-green-600 focus:ring-green-200"
                                    />
                                    Previsto
                                </label>

                                <label className="flex items-center gap-1.5 text-sm cursor-pointer">
                                    <input
                                        type="checkbox"
                                        checked={showRealized}
                                        onChange={(e) => setShowRealized(e.target.checked)}
                                        className="rounded border-green-300 text-green-600 focus:ring-green-200"
                                    />
                                    Realizado
                                </label>
                            </div>
                        </div>
                    </CardDescription>
                </CardHeader>
                <CardContent className={`${isMobile ? 'h-[400px]' : 'h-[500px]'}`}>
                    <ChartContainer
                        config={chartConfig}
                        className="h-full w-full"
                    >
                        <BarChart
                            data={transformedData}
                            margin={isMobile ? { top: 20, right: 5, left: 0, bottom: 5 } : { top: 30, right: 30, left: 0, bottom: 5 }}
                        >
                            <CartesianGrid vertical={false} horizontal={true} strokeDasharray="3 3" />
                            <XAxis
                                dataKey="name"
                                tickLine={false}
                                tickMargin={isMobile ? 5 : 10}
                                axisLine={true}
                                tick={{ fontSize: isMobile ? 10 : 12 }}
                                interval={isMobile ? 1 : 0}
                            />
                            <YAxis
                                type="number"
                                tickLine={true}
                                axisLine={true}
                                tickMargin={isMobile ? 5 : 10}
                                width={isMobile ? 40 : 50}
                                domain={[0, (dataMax: number) => {
                                    // CRITICAL FIX 3: Ajustar a escala para ser 10% acima do maior valor, sem exceder 100%
                                    const maxValue = Math.max(dataMax, 1); // Evitar divisão por zero
                                    const scaledMax = Math.min(Math.ceil(maxValue * 1.1), 100);
                                    console.log(`CRITICAL FIX 3: Y-axis scale - dataMax: ${dataMax}, scaledMax: ${scaledMax}`);
                                    return scaledMax;
                                }]}
                                unit="%"
                                tick={{ fontSize: isMobile ? 10 : 12 }}
                            />
                            <ChartTooltip
                                content={({ active, payload, label }) => {
                                    if (active && payload && payload.length > 0) {
                                        // CRITICAL FIX 2: Definir o tipo para serviceGroups
                                        interface ServiceGroup {
                                            name: string;
                                            displayName: string;
                                            predicted: number | null;
                                            realized: number | null;
                                            predictedColor: string | null;
                                            realizedColor: string | null;
                                        }

                                        // CRITICAL FIX 5: Agrupar os dados por serviço (removendo o sufixo 'Pred')
                                        const serviceGroups: Record<string, ServiceGroup> = {};

                                        console.log('CRITICAL FIX 5: Tooltip payload:', payload);

                                        // Filtrar apenas as entradas com valores maiores que zero
                                        const filteredPayload = payload.filter(entry => {
                                            const value = entry.value as number;
                                            return value > 0;
                                        });

                                        console.log('CRITICAL FIX 5: Filtered tooltip payload (values > 0):', filteredPayload);

                                        filteredPayload.forEach(entry => {
                                            // Verificar se dataKey existe
                                            if (!entry.dataKey) {
                                                console.log('CRITICAL FIX 5: Entry without dataKey:', entry);
                                                return;
                                            }

                                            const dataKey = entry.dataKey.toString();
                                            const isPredicted = dataKey.endsWith('Pred');

                                            // Extrair o nome do serviço (removendo o sufixo 'Pred' se necessário)
                                            const serviceName = isPredicted
                                                ? dataKey.replace(/Pred$/, '')
                                                : dataKey;

                                            // Verificar se é um serviço com sufixo único
                                            const isUniqueService = serviceName.includes('_');

                                            // Obter o nome base (sem o sufixo único)
                                            const baseServiceName = isUniqueService
                                                ? serviceName.split('_')[0]
                                                : serviceName;

                                            // Formatar o nome para exibição (primeira letra maiúscula e com acentos)
                                            const displayName = formatServiceName(baseServiceName);

                                            // Usar o nome de exibição como chave para agrupar serviços com o mesmo nome base
                                            const groupKey = displayName;

                                            console.log(`CRITICAL FIX 5: Processing tooltip entry - dataKey: ${dataKey}, serviceName: ${serviceName}, displayName: ${displayName}, isPredicted: ${isPredicted}, value: ${entry.value}`);

                                            if (!serviceGroups[groupKey]) {
                                                serviceGroups[groupKey] = {
                                                    name: serviceName,
                                                    displayName: displayName,
                                                    predicted: null,
                                                    realized: null,
                                                    predictedColor: null,
                                                    realizedColor: null
                                                };
                                            }

                                            if (isPredicted) {
                                                // Se já temos um valor previsto para este grupo, somar ao existente
                                                const currentValue = serviceGroups[groupKey].predicted || 0;
                                                serviceGroups[groupKey].predicted = currentValue + (entry.value as number);
                                                serviceGroups[groupKey].predictedColor = entry.color as string;
                                            } else {
                                                // Se já temos um valor realizado para este grupo, somar ao existente
                                                const currentValue = serviceGroups[groupKey].realized || 0;
                                                serviceGroups[groupKey].realized = currentValue + (entry.value as number);
                                                serviceGroups[groupKey].realizedColor = entry.color as string;
                                            }
                                        });

                                        console.log('CRITICAL FIX 5: Grouped services for tooltip:', serviceGroups);

                                        return (
                                            <div className="bg-white p-3 rounded-md shadow-md border border-gray-200 max-w-xs">
                                                <div className="font-bold text-sm border-b pb-1 mb-2">
                                                    Período: {label}
                                                </div>

                                                <div className="flex flex-col gap-3">
                                                    {Object.values(serviceGroups).map((service: ServiceGroup, index) => (
                                                        <div key={index} className="flex flex-col gap-1">
                                                            <div className="font-semibold text-sm uppercase">
                                                                {service.displayName}
                                                            </div>
                                                            <div className="flex flex-col gap-1 pl-2">
                                                                {service.predicted !== null && showPredicted && (
                                                                    <div className="flex items-center gap-2">
                                                                        <div
                                                                            className="w-3 h-3 rounded-sm"
                                                                            style={{
                                                                                backgroundColor: (() => {
                                                                                    // Usar as cores do mapa de cores
                                                                                    const colorInfo = serviceColorMap.get(service.displayName);
                                                                                    return colorInfo?.light || 'rgb(146, 219, 171)';
                                                                                })()
                                                                            }}
                                                                        />
                                                                        <span className="text-xs">
                                                                            Previsto: <span className="font-bold">{service.predicted}%</span>
                                                                        </span>
                                                                    </div>
                                                                )}
                                                                {service.realized !== null && showRealized && (
                                                                    <div className="flex items-center gap-2">
                                                                        <div
                                                                            className="w-3 h-3 rounded-sm"
                                                                            style={{
                                                                                backgroundColor: (() => {
                                                                                    // Usar as cores do mapa de cores
                                                                                    const colorInfo = serviceColorMap.get(service.displayName);
                                                                                    return colorInfo?.main || 'rgb(22, 163, 74)';
                                                                                })()
                                                                            }}
                                                                        />
                                                                        <span className="text-xs">
                                                                            Realizado: <span className="font-bold">{service.realized}%</span>
                                                                        </span>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        );
                                    }

                                    return null;
                                }}
                                cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                            />
                            {/* Legenda simplificada */}
                            <div className="absolute top-2 right-2 p-3 bg-white rounded-md shadow-md border border-gray-200 z-10">
                                <div className="flex flex-col gap-3">
                                    <div className="text-xs font-bold text-gray-700 border-b pb-1 mb-1">LEGENDA</div>

                                    {/* CRITICAL FIX 5: Legenda de serviços com cores para realizado e previsto */}
                                    <div className="flex flex-col gap-3">
                                        {/* CRITICAL FIX 5: Identificar quais serviços estão presentes nos dados transformados */}
                                        {(() => {
                                            // Extrair todos os serviços presentes nos dados transformados
                                            const servicesInData = new Set<string>();

                                            // Para cada período nos dados transformados
                                            transformedData.forEach(periodData => {
                                                // Para cada chave no período (exceto 'name')
                                                Object.keys(periodData).forEach(key => {
                                                    if (key !== 'name' && !key.endsWith('Pred')) {
                                                        // Adicionar o serviço ao conjunto
                                                        servicesInData.add(key);
                                                    }
                                                });
                                            });

                                            console.log('CRITICAL FIX 5: Services in transformed data:', Array.from(servicesInData));

                                            // CRITICAL FIX 6: Filtrar os serviços com base na atividade selecionada
                                            const filteredServices = selectedActivity === 'all'
                                                ? Array.from(servicesInData)
                                                : [selectedActivity].filter(service => servicesInData.has(service));

                                            console.log(`CRITICAL FIX 6: Filtered services for legend based on selection '${selectedActivity}':`, filteredServices);

                                            // Renderizar a legenda apenas para os serviços filtrados
                                            return filteredServices.map((serviceName, index) => {
                                                // Obter as cores para este serviço
                                                const colorInfo = serviceColorMap.get(serviceName);
                                                const realColor = colorInfo?.main || 'rgb(22, 163, 74)';
                                                const predColor = colorInfo?.light || 'rgb(146, 219, 171)';

                                                // Exibir o nome do serviço formatado (primeira letra maiúscula e com acentos)
                                                const baseServiceName = serviceName.includes('_')
                                                    ? serviceName.split('_')[0]
                                                    : serviceName;

                                                // Formatar o nome para exibição
                                                const displayName = formatServiceName(baseServiceName);

                                                return (
                                                    <div key={index} className="flex flex-col gap-1">
                                                        <div className="text-xs font-semibold uppercase">{displayName}</div>
                                                        <div className="flex items-center gap-3">
                                                            {showRealized && (
                                                                <div className="flex items-center gap-1">
                                                                    <div
                                                                        className="w-8 h-4 rounded-sm"
                                                                        style={{ backgroundColor: realColor }}
                                                                    />
                                                                    <span className="text-xs">Realizado</span>
                                                                </div>
                                                            )}
                                                            {showPredicted && (
                                                                <div className="flex items-center gap-1">
                                                                    <div
                                                                        className="w-8 h-4 rounded-sm"
                                                                        style={{ backgroundColor: predColor }}
                                                                    />
                                                                    <span className="text-xs">Previsto</span>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                );
                                            });
                                        })()}
                                    </div>
                                </div>
                            </div>

                            {/* Renderizar todas as barras - primeiro previsto (esquerda), depois realizado (direita) */}
                            {/* CRITICAL FIX 5: Renderizar apenas as barras para serviços com valores */}
                            {
                                (() => {
                                    // Extrair todos os serviços presentes nos dados transformados
                                    const servicesInData = new Set<string>();

                                    // Para cada período nos dados transformados
                                    transformedData.forEach(periodData => {
                                        // Para cada chave no período (exceto 'name')
                                        Object.keys(periodData).forEach(key => {
                                            if (key !== 'name' && !key.endsWith('Pred')) {
                                                // Verificar se o valor é maior que zero
                                                const value = periodData[key];
                                                if (value > 0) {
                                                    // Adicionar o serviço ao conjunto
                                                    servicesInData.add(key);
                                                }
                                            }
                                        });
                                    });

                                    console.log('CRITICAL FIX 5: Services with values > 0 in transformed data:', Array.from(servicesInData));

                                    // CRITICAL FIX 6: Filtrar os serviços com base na atividade selecionada
                                    const filteredServices = selectedActivity === 'all'
                                        ? Array.from(servicesInData)
                                        : [selectedActivity].filter(service => servicesInData.has(service));

                                    console.log(`CRITICAL FIX 6: Filtered services based on selection '${selectedActivity}':`, filteredServices);

                                    // Renderizar as barras apenas para os serviços filtrados
                                    return filteredServices.map((serviceName, serviceIndex) => {
                                        // Verificar se é um serviço com sufixo único (adicionado para evitar duplicatas)
                                        const isUniqueService = serviceName.includes('_');

                                        // Para serviços com sufixo único, exibir um nome mais amigável no tooltip
                                        const displayName = isUniqueService
                                            ? serviceName.split('_')[0] // Usar apenas a parte antes do underscore
                                            : serviceName;

                                        console.log(`CRITICAL FIX 6: Rendering bar for service: ${serviceName}, display name: ${displayName}`);

                                        // Para cada serviço, renderizar primeiro o previsto, depois o realizado
                                        return (
                                            <React.Fragment key={`service-${serviceIndex}`}>
                                                {/* Barra de valor previsto (esquerda) - só renderiza se showPredicted for true */}
                                                {showPredicted && (
                                                    <Bar
                                                        key={`pred-${serviceIndex}`}
                                                        dataKey={`${serviceName}Pred`}
                                                        name={`${displayName} (previsto)`}
                                                        fill={findColor(`${serviceName}Pred`, 'LIGHT_COLOR') || 'rgb(59 130 246)'}
                                                        radius={[4, 4, 0, 0]}
                                                        barSize={isMobile ? 10 : 14}
                                                        stroke="rgba(0,0,0,0.1)"
                                                        strokeWidth={1}
                                                    />
                                                )}

                                                {/* Barra de valor realizado (direita) - só renderiza se showRealized for true */}
                                                {showRealized && (
                                                    <Bar
                                                        key={`real-${serviceIndex}`}
                                                        dataKey={serviceName}
                                                        name={displayName}
                                                        fill={findColor(serviceName, 'COLOR') || 'rgb(22 163 74)'}
                                                        radius={[4, 4, 0, 0]}
                                                        barSize={isMobile ? 10 : 14}
                                                        stroke="rgba(0,0,0,0.1)"
                                                        strokeWidth={1}
                                                    />
                                                )}
                                            </React.Fragment>
                                        );
                                    });
                                })()
                            }
                        </BarChart>
                    </ChartContainer>
                </CardContent>
            </Card>

            {/* Removido o modal de relatório, agora geramos o Excel diretamente */}
        </div>
    );
}