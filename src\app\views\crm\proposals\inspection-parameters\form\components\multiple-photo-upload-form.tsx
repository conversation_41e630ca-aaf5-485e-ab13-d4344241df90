import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { X, Image as ImageIcon, ChevronUp, ChevronDown, GripVertical, Camera, RefreshCcw } from "lucide-react";
import Image from "next/image";
import { Label } from "@/src/components/ui/label";
import { Input } from "@/src/components/ui/input";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/src/components/ui/dialog";

/**
 * Componente para upload múltiplo de fotos com suporte a reordenação
 *
 * Este componente permite:
 * 1. Selecionar múltiplas imagens
 * 2. Adicionar legendas para cada imagem
 * 3. Reordenar as imagens usando botões para mover para cima/baixo
 * 4. Reordenar as imagens usando drag and drop
 * 5. <PERSON><PERSON> as pré-visualizações consistentes durante a reordenação
 *
 * A solução implementada usa um identificador único para cada arquivo
 * e recria as URLs de blob quando necessário para garantir que as
 * pré-visualizações sejam mantidas durante a reordenação.
 *
 * Estilos CSS para drag and drop:
 * - Durante o arrasto, o item arrastado recebe uma rotação e sombra
 * - A área de drop muda de cor quando um item está sendo arrastado sobre ela
 * - O cursor muda para "grab" quando o mouse está sobre a alça de arrasto
 */

// Estilos CSS personalizados
const customStyles = `
  .dragging-item {
    pointer-events: auto !important;
    cursor: grabbing !important;
    opacity: 0.9 !important;
    z-index: 9999 !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  }

  .drop-area {
    transition: all 0.2s ease;
    min-height: 100px;
    border-radius: 0.5rem;
    position: relative;
    z-index: 1;
  }

  .drop-area.active {
    background-color: rgba(235, 245, 255, 0.3);
    border: 2px dashed #90cdf4;
    padding: 0.5rem;
    margin: -0.5rem;
  }

  .drag-handle {
    cursor: grab;
  }

  .drag-handle:active {
    cursor: grabbing;
  }
`;

// Interface para representar um arquivo com descrição e ID único
interface FileWithMetadata {
  file: File;
  description: string;
  id: string; // ID único para identificar o arquivo
}

interface MultiplePhotoUploadFormProps {
  onSave: (selectedFiles: { file: File; description: string }[], globalDescription?: string) => void;
  onCancel: () => void;
}

export default function MultiplePhotoUploadForm({
  onSave,
  onCancel
}: MultiplePhotoUploadFormProps) {
  const [selectedFiles, setSelectedFiles] = useState<FileWithMetadata[]>([]);
  const [previewUrls, setPreviewUrls] = useState<{ [key: string]: string }>({});
  const [errors, setErrors] = useState<{ [key: number]: string }>({});
  const [isDragging, setIsDragging] = useState(false);

  // Estados para a captura de câmera via API getUserMedia
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment'); // 'environment' é a câmera traseira
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Novo estado para armazenar múltiplas fotos tiradas na sessão da câmera
  const [cameraPhotos, setCameraPhotos] = useState<FileWithMetadata[]>([]);
  const [cameraPreviewUrls, setCameraPreviewUrls] = useState<{ [key: string]: string }>({});

  // Novo estado para a legenda global
  const [globalDescription, setGlobalDescription] = useState<string>("");

  // Aplicar estilos CSS personalizados
  useEffect(() => {
    // Criar elemento de estilo
    const styleElement = document.createElement('style');
    styleElement.innerHTML = customStyles;
    document.head.appendChild(styleElement);

    // Limpar ao desmontar
    return () => {
      // Remover estilos
      document.head.removeChild(styleElement);

      // Revogar todas as URLs de blob ao desmontar o componente
      Object.values(previewUrls).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [previewUrls]);

  // Limpar recursos da câmera quando o componente for desmontado
  useEffect(() => {
    return () => {
      // Parar todas as trilhas de vídeo se houver um stream ativo
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      // Revogar blobs das fotos tiradas na câmera (temporários)
      Object.values(cameraPreviewUrls).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
      // Revogar blobs das fotos adicionadas ao formulário principal
      Object.values(previewUrls).forEach(url => {
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [stream, cameraPreviewUrls, previewUrls]);

  // Função para gerar um ID único
  const generateUniqueId = () => {
    return `file-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  };

  // Função para gerar preview base64 de um arquivo
  const getFileDataUrl = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  // Função para selecionar arquivos da galeria (gera preview base64)
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);

      // Criar objetos para cada arquivo com um campo de descrição vazio e ID único
      const newFiles = filesArray.map(file => ({
        file,
        description: "",
        id: generateUniqueId()
      }));

      // Gerar previews base64
      const newPreviewUrlsObj: { [key: string]: string } = {};
      await Promise.all(
        newFiles.map(async fileItem => {
          newPreviewUrlsObj[fileItem.id] = await getFileDataUrl(fileItem.file);
        })
      );

      setSelectedFiles([...selectedFiles, ...newFiles]);
      setPreviewUrls({ ...previewUrls, ...newPreviewUrlsObj });
    }
  };

  const handleDescriptionChange = (index: number, value: string) => {
    const updatedFiles = [...selectedFiles];
    updatedFiles[index].description = value;
    setSelectedFiles(updatedFiles);

    // Limpar erro se o campo foi preenchido
    if (value.trim().length > 0 && errors[index]) {
      const newErrors = { ...errors };
      delete newErrors[index];
      setErrors(newErrors);
    }
  };

  const handleRemoveFile = (index: number) => {
    try {
      // Obter o arquivo que está sendo removido
      const fileToRemove = selectedFiles[index];

      // Revogar URL de preview
      if (previewUrls[fileToRemove.id]) {
        URL.revokeObjectURL(previewUrls[fileToRemove.id]);
      }

      // Criar uma cópia dos objetos de estado
      const newFiles = selectedFiles.filter((_, i) => i !== index);
      const newPreviewUrlsObj = { ...previewUrls };

      // Remover a URL de preview do objeto
      delete newPreviewUrlsObj[fileToRemove.id];

      // Remover erro associado
      const newErrors = { ...errors };
      delete newErrors[index];

      // Reindexar os erros para corresponder aos novos índices
      const reindexedErrors: { [key: number]: string } = {};
      Object.keys(newErrors).forEach(key => {
        const numKey = parseInt(key);
        if (numKey > index) {
          reindexedErrors[numKey - 1] = newErrors[numKey];
        } else {
          reindexedErrors[numKey] = newErrors[numKey];
        }
      });

      // Atualizar os estados
      setSelectedFiles(newFiles);
      setPreviewUrls(newPreviewUrlsObj);
      setErrors(reindexedErrors);
    } catch (error) {
      console.error("Erro ao remover arquivo:", error);
    }
  };

  // Abordagem mais robusta para reordenamento

  // Função para mover uma imagem para cima (diminuir o índice)
  const handleMoveUp = (index: number) => {
    if (index <= 0) return; // Não pode mover para cima se já estiver no topo

    try {
      // 1. Criar cópias dos arrays
      const newFiles = [...selectedFiles];

      // 2. Mover o item para cima (trocar com o item acima)
      const itemToMove = newFiles[index];
      newFiles.splice(index, 1); // Remover da posição atual
      newFiles.splice(index - 1, 0, itemToMove); // Inserir na nova posição

      // 3. Atualizar o estado dos arquivos
      setSelectedFiles(newFiles);

      // 4. Não precisamos atualizar as URLs de preview, pois elas são indexadas pelo ID do arquivo
      // que permanece o mesmo durante a reordenação

      // 5. Atualizar os erros, se houver
      if (Object.keys(errors).length > 0) {
        const newErrors: { [key: number]: string } = {};

        // Mapear os erros para as novas posições
        Object.entries(errors).forEach(([key, value]) => {
          const errorIndex = parseInt(key);

          if (errorIndex === index) {
            // O erro estava no item movido
            newErrors[index - 1] = value;
          } else if (errorIndex === index - 1) {
            // O erro estava no item que foi trocado
            newErrors[index] = value;
          } else {
            // Outros erros permanecem na mesma posição
            newErrors[errorIndex] = value;
          }
        });

        setErrors(newErrors);
      }
    } catch (error) {
      console.error("Erro ao mover imagem para cima:", error);
    }
  };

  // Função para mover uma imagem para baixo (aumentar o índice)
  const handleMoveDown = (index: number) => {
    if (index >= selectedFiles.length - 1) return; // Não pode mover para baixo se já estiver no final

    try {
      // 1. Criar cópias dos arrays
      const newFiles = [...selectedFiles];

      // 2. Mover o item para baixo (trocar com o item abaixo)
      const itemToMove = newFiles[index];
      newFiles.splice(index, 1); // Remover da posição atual
      newFiles.splice(index + 1, 0, itemToMove); // Inserir na nova posição

      // 3. Atualizar o estado dos arquivos
      setSelectedFiles(newFiles);

      // 4. Não precisamos atualizar as URLs de preview, pois elas são indexadas pelo ID do arquivo
      // que permanece o mesmo durante a reordenação

      // 5. Atualizar os erros, se houver
      if (Object.keys(errors).length > 0) {
        const newErrors: { [key: number]: string } = {};

        // Mapear os erros para as novas posições
        Object.entries(errors).forEach(([key, value]) => {
          const errorIndex = parseInt(key);

          if (errorIndex === index) {
            // O erro estava no item movido
            newErrors[index + 1] = value;
          } else if (errorIndex === index + 1) {
            // O erro estava no item que foi trocado
            newErrors[index] = value;
          } else {
            // Outros erros permanecem na mesma posição
            newErrors[errorIndex] = value;
          }
        });

        setErrors(newErrors);
      }
    } catch (error) {
      console.error("Erro ao mover imagem para baixo:", error);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: number]: string } = {};
    let isValid = true;

    selectedFiles.forEach((item, index) => {
      if (!item.description.trim()) {
        newErrors[index] = "Legenda é obrigatória";
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = () => {
    if (validateForm()) {
      // Converter o formato dos arquivos para o formato esperado pelo componente pai
      const filesForSave = selectedFiles.map(item => ({
        file: item.file,
        description: item.description
      }));

      onSave(filesForSave, globalDescription);
    }
  };

  // Função para lidar com o início do arrasto
  const handleDragStart = () => {
    setIsDragging(true);
    // Adicionar classe ao body para indicar que está arrastando
    document.body.classList.add('dragging');
  };

  // Função para lidar com o fim do arrasto
  const handleDragEnd = (result: DropResult) => {
    // Remover classe do body
    document.body.classList.remove('dragging');
    // Resetar o estado de arrasto
    setIsDragging(false);

    const { source, destination } = result;

    // Se não houver destino ou se a posição não mudou, não faz nada
    if (!destination) {
      // O usuário soltou o item fora da área de drop
      return;
    }

    if (source.index === destination.index) {
      // A posição não mudou
      return;
    }

    try {
      // Criar uma cópia do array de arquivos
      const newFiles = Array.from(selectedFiles);

      // Remover o item da posição original e inserir na nova posição
      const [movedItem] = newFiles.splice(source.index, 1);
      newFiles.splice(destination.index, 0, movedItem);

      // Atualizar o estado
      setSelectedFiles(newFiles);

      // Atualizar os erros, se houver
      if (Object.keys(errors).length > 0) {
        const newErrors: { [key: number]: string } = {};

        // Mapear os erros para as novas posições
        Object.entries(errors).forEach(([key, value]) => {
          const errorIndex = parseInt(key);

          if (errorIndex === source.index) {
            // O erro estava no item movido
            newErrors[destination.index] = value;
          } else if (errorIndex > source.index && errorIndex <= destination.index) {
            // Os erros entre a posição original e a nova posição devem ser deslocados para cima
            newErrors[errorIndex - 1] = value;
          } else if (errorIndex < source.index && errorIndex >= destination.index) {
            // Os erros entre a nova posição e a posição original devem ser deslocados para baixo
            newErrors[errorIndex + 1] = value;
          } else {
            // Outros erros permanecem na mesma posição
            newErrors[errorIndex] = value;
          }
        });

        setErrors(newErrors);
      }
    } catch (error) {
      console.error("Erro ao reordenar por drag and drop:", error);
    }
  };

  // Função para verificar se é um dispositivo móvel
  const isMobileDevice = () => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  // Função para abrir a câmera
  const openCamera = async () => {
    // Verificar se é um dispositivo desktop
    if (!isMobileDevice()) {
      // Em desktop, o botão deveria estar oculto, mas por segurança mostramos uma mensagem
      alert("A funcionalidade de câmera é otimizada para dispositivos móveis. Por favor, use a opção 'Selecionar da galeria' em computadores desktop.");
      return;
    }

    try {
      // Verificar se o navegador suporta a API getUserMedia
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert("Seu navegador não suporta acesso à câmera. Por favor, use um navegador mais recente ou um dispositivo móvel.");
        return;
      }

      // Solicitar acesso à câmera com o facingMode atual
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: facingMode
        },
        audio: false
      });

      // Armazenar o stream e abrir o diálogo da câmera
      setStream(mediaStream);
      setIsCameraOpen(true);

      // Conectar o stream ao elemento de vídeo quando o diálogo for aberto
      setTimeout(() => {
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
          videoRef.current.play();
        }
      }, 100);
    } catch (error) {
      console.error("Erro ao acessar a câmera:", error);
      alert("Não foi possível acessar a câmera. Verifique as permissões do navegador.");
    }
  };

  // Função para alternar entre câmera frontal e traseira
  const toggleCamera = async () => {
    // Fechar o stream atual
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }

    // Alternar o facingMode
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user';
    setFacingMode(newFacingMode);

    try {
      // Abrir a câmera com o novo facingMode
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: newFacingMode
        },
        audio: false
      });

      // Atualizar o stream
      setStream(mediaStream);

      // Atualizar o vídeo
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.play();
      }
    } catch (error) {
      console.error("Erro ao alternar câmera:", error);
      alert("Não foi possível alternar entre as câmeras. Seu dispositivo pode ter apenas uma câmera disponível.");
    }
  };

  // Função para fechar a câmera
  const closeCamera = () => {
    // Parar todas as trilhas de vídeo
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    // Revogar blobs das fotos tiradas na câmera
    Object.values(cameraPreviewUrls).forEach(url => {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
    });
    // Resetar o estado
    setStream(null);
    setIsCameraOpen(false);
    setCameraPhotos([]);
    setCameraPreviewUrls({});
  };

  const reativarCamera = async () => {
    try {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode },
        audio: false
      });

      setStream(mediaStream);

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        await videoRef.current.play();
      }
    } catch (error) {
      console.error("Erro ao reativar a câmera:", error);
      alert("Não foi possível reativar a câmera.");
    }
  };



  // Função robusta para capturar uma foto da câmera (gera preview base64)
  const capturePhoto = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;

    // Garante que o vídeo está pronto
    if (video.readyState < 2) {
      await new Promise<void>(resolve => {
        const handler = () => {
          video.removeEventListener('loadeddata', handler);
          resolve();
        };
        video.addEventListener('loadeddata', handler);
      });
    }

    // Garante que o vídeo está rodando antes de capturar
    if (video.paused || video.ended) {
      try {
        await video.play();
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (err) {
        console.error("Erro ao tentar rodar o vídeo antes de capturar:", err);
        return;
      }
    }

    // Ajusta o tamanho do canvas
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Captura o frame atual do vídeo
    const context = canvas.getContext('2d');
    if (!context) return;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Aguarda um frame para garantir que o vídeo não fique escuro
    await new Promise(requestAnimationFrame);

    // Gera DataURL do canvas
    const dataUrl = canvas.toDataURL('image/jpeg', 0.95);

    // Converte para blob e cria arquivo
    const fileName = `camera-photo-${Date.now()}.jpg`;
    const blob = await (await fetch(dataUrl)).blob();
    const file = new File([blob], fileName, { type: 'image/jpeg' });

    // Adiciona à lista temporária de fotos
    const newFileItem: FileWithMetadata = {
      file,
      description: "",
      id: generateUniqueId()
    };

    setCameraPhotos(prev => [...prev, newFileItem]);
    setCameraPreviewUrls(prev => ({ ...prev, [newFileItem.id]: dataUrl }));

    // Garante que o vídeo continua rodando após capturar
    setTimeout(() => {
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.play().catch(err => {
          console.error("Erro ao tentar continuar vídeo após captura:", err);
        });
      }
    }, 100);
  };


  // Função para adicionar todas as fotos tiradas na câmera ao formulário principal
  const handleAddCameraPhotos = () => {
    // Copia os previews base64 para o preview principal
    setSelectedFiles(prev => [...prev, ...cameraPhotos]);
    setPreviewUrls(prev => ({ ...prev, ...cameraPreviewUrls }));

    setIsCameraOpen(false);
    setCameraPhotos([]);
    setCameraPreviewUrls({});
    // if (stream) {
    //   stream.getTracks().forEach(track => track.stop());
    // }
    // setStream(null);
  };

  // Função para remover foto da lista temporária da câmera
  const handleRemoveCameraPhoto = (index: number) => {
    const fileToRemove = cameraPhotos[index];
    const newPhotos = cameraPhotos.filter((_, i) => i !== index);
    const newPreviewUrls = { ...cameraPreviewUrls };
    delete newPreviewUrls[fileToRemove.id];
    setCameraPhotos(newPhotos);
    setCameraPreviewUrls(newPreviewUrls);
  };

  // Função para aplicar a descrição global a todas as fotos selecionadas
  const handleApplyGlobalDescription = () => {
    if (!globalDescription) return;
    const updated = selectedFiles.map(item => ({
      ...item,
      description: item.description?.trim() ? item.description : globalDescription
    }));
    setSelectedFiles(updated);
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Input de arquivo para galeria (oculto, acionado pelo botão) */}
      <input
        type="file"
        id="multiple-photos"
        className="hidden"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
      />

      {/* Botões para capturar/selecionar imagens */}
      <div className="flex flex-col sm:flex-row gap-2">
        {/* Botão de câmera - visível apenas em dispositivos móveis */}
        <Button
          type="button"
          onClick={openCamera}
          className="bg-green-500 hover:bg-green-600 w-full sm:hidden"
        >
          Tirar foto com a câmera
        </Button>
        <Button
          type="button"
          onClick={() => document.getElementById("multiple-photos")?.click()}
          className="bg-blue-500 hover:bg-blue-600 w-full sm:w-auto"
        >
          Selecionar da galeria
        </Button>
      </div>

      {/* Diálogo da câmera - tela cheia sem scroll */}
      <Dialog open={isCameraOpen} onOpenChange={(open) => !open && closeCamera()}>
        <DialogContent className="max-w-full w-full h-[100dvh] p-0 m-0 rounded-none border-0 overflow-hidden sm:rounded-lg sm:border sm:h-auto sm:max-h-[90vh] sm:p-6 sm:m-4">
          {/* Cabeçalho fixo no topo */}
          <div className="absolute top-0 left-0 right-0 z-10 bg-background shadow-md">
            <DialogHeader className="p-4 sm:p-0">
              <DialogTitle className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <Camera className="h-5 w-5" />
                  <span>Tirar foto com a câmera</span>
                </div>
              </DialogTitle>

              {/* Indicador e botão abaixo do título */}
              <div className="flex items-center justify-between mt-4 pt-2 border-t">
                <span className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                  {facingMode === 'user' ? 'Câmera Frontal' : 'Câmera Traseira'}
                </span>
                <Button
                  type="button"
                  onClick={toggleCamera}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  title={facingMode === 'user' ? 'Alternar para câmera traseira' : 'Alternar para câmera frontal'}
                >
                  <RefreshCcw className="h-4 w-4" />
                  <span>Alternar câmera</span>
                </Button>
              </div>
            </DialogHeader>
          </div>

          {/* Conteúdo principal - ocupa todo o espaço disponível */}
          <div className="flex flex-col h-full pt-28 pb-20 sm:pt-0 sm:pb-0 sm:h-auto">
            {/* Elemento de vídeo para exibir o feed da câmera - ocupa todo o espaço disponível */}
            <div className="relative bg-black flex-grow h-full">
              <video
                ref={videoRef}
                className="absolute inset-0 w-full h-full object-cover"
                autoPlay
                playsInline
              />
            </div>

            {/* Canvas oculto para capturar a imagem */}
            <canvas ref={canvasRef} className="hidden" />

            {/* Lista de fotos tiradas na sessão da câmera */}
            {cameraPhotos.length > 0 && (
              <div className="mt-4 max-h-40 overflow-x-auto flex gap-3 px-2">
                {cameraPhotos.map((item, idx) => (
                  <div key={item.id} className="relative w-20 h-20 rounded-md overflow-hidden border border-gray-200 flex-shrink-0">
                    {cameraPreviewUrls[item.id] ? (
                      <Image
                        src={cameraPreviewUrls[item.id]}
                        alt={`Preview ${idx}`}
                        fill
                        className="object-cover rounded-md"
                        unoptimized
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full w-full">
                        <ImageIcon className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                    <button
                      type="button"
                      className="absolute top-0 right-0 bg-white/80 rounded-bl px-1 py-0.5 text-xs text-red-600"
                      onClick={() => handleRemoveCameraPhoto(idx)}
                      title="Remover foto"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Botões de ação - fixados na parte inferior */}
          <div className="absolute bottom-0 left-0 right-0 z-10 bg-background p-4 sm:static sm:p-0 sm:mt-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                type="button"
                onClick={closeCamera}
                className="bg-blue-500 hover:bg-blue-600 w-full sm:w-auto text-white"
              >
                Cancelar
              </Button>
              <Button
                type="button"
                onClick={async () => {
                  await capturePhoto();
                  await reativarCamera();
                }}
                className="bg-green-500 hover:bg-green-600 w-full sm:flex-grow"
              >
                Capturar foto
              </Button>
              <Button
                type="button"
                onClick={handleAddCameraPhotos}
                className="bg-green-700 hover:bg-green-800 w-full sm:w-auto"
                disabled={cameraPhotos.length === 0}
              >
                Adicionar {cameraPhotos.length} foto{cameraPhotos.length !== 1 ? 's' : ''}
              </Button>

            </div>

          </div>
        </DialogContent>
      </Dialog>

      {/* Campo para legenda global com botão de aplicar */}
      <div className="mb-4 flex items-center gap-2">
        <div className="flex-1">
          <Label className="block text-sm font-medium text-gray-700 mb-1">
            Digite uma legenda para todos
          </Label>
          <Input
            type="text"
            value={globalDescription}
            onChange={e => setGlobalDescription(e.target.value)}
            placeholder="Legenda para todas as imagens vazias"
            className="w-full"
          />
        </div>
        <Button
          type="button"
          className="mt-6 px-3 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50"
          onClick={handleApplyGlobalDescription}
          title="Aplicar legenda para todos os campos vazios"
          disabled={selectedFiles.length === 0}
        >
          Aplicar
        </Button>
      </div>

      {/* Lista de arquivos selecionados */}
      {selectedFiles.length > 0 ? (
        <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
          <Droppable droppableId="photos">
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`mt-4 space-y-4 max-h-[400px] overflow-y-auto overflow-x-hidden p-2
                  drop-area ${snapshot.isDraggingOver ? 'active' : ''}
                  ${isDragging ? 'min-height-200 relative' : ''}
                `}

              >
                {selectedFiles.map((item, index) => (
                  <Draggable key={item.id} draggableId={item.id} index={index}>
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={`flex flex-col sm:flex-row gap-4 p-3 border rounded-md transition-all duration-200
                          ${snapshot.isDragging ? 'dragging-item' : 'hover:border-gray-300'}`}

                        style={{
                          ...provided.draggableProps.style,
                          transform: snapshot.isDragging
                            ? `${provided.draggableProps.style?.transform} rotate(2deg) scale(1.02)`
                            : provided.draggableProps.style?.transform,
                          opacity: snapshot.isDragging ? 0.9 : 1,
                        }}
                      >
                        {/* Cabeçalho para dispositivos móveis */}
                        <div className="flex justify-between items-center sm:hidden w-full mb-2">
                          <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-700 rounded-full font-bold text-sm flex-shrink-0">
                              {index + 1}
                            </div>
                            <div
                              {...provided.dragHandleProps}
                              className="drag-handle text-gray-400 hover:text-gray-600"
                              title="Arrastar para reordenar"
                            >
                              <GripVertical className="w-5 h-5" />
                            </div>
                          </div>

                          {/* Botões de ação para mobile */}
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                              onClick={() => handleMoveUp(index)}
                              disabled={index === 0}
                              title="Mover para cima"
                            >
                              <ChevronUp className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                              onClick={() => handleMoveDown(index)}
                              disabled={index === selectedFiles.length - 1}
                              title="Mover para baixo"
                            >
                              <ChevronDown className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-500 hover:bg-red-50 hover:text-red-500 h-8 w-8 flex-shrink-0"
                              onClick={() => handleRemoveFile(index)}
                              title="Remover"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Layout para dispositivos móveis */}
                        <div className="flex flex-col gap-3 sm:hidden w-full">
                          {/* Preview da imagem e legenda em layout móvel */}
                          <div className="flex gap-3">
                            {/* Preview da imagem */}
                            <div className="relative w-20 h-20 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden">
                              {previewUrls[item.id] ? (
                                <Image
                                  src={previewUrls[item.id]}
                                  alt={`Preview ${index}`}
                                  fill
                                  className="object-cover rounded-md"
                                  unoptimized
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full w-full">
                                  <ImageIcon className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </div>

                            {/* Campo de legenda para mobile */}
                            <div className="flex-grow min-w-0">
                              <div className="flex flex-col gap-1">
                                <Label
                                  htmlFor={`photo-caption-mobile-${index}`}
                                  className="font-bold text-gray-700 text-sm truncate"
                                >
                                  Legenda
                                </Label>
                                <Input
                                  id={`photo-caption-mobile-${index}`}
                                  value={item.description}
                                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleDescriptionChange(index, e.target.value)}
                                  placeholder="Digite uma legenda"
                                  className={`h-9 ${errors[index] ? "border-red-500" : ""}`}
                                />
                                {errors[index] && (
                                  <small className="text-xs font-semibold text-destructive">
                                    {errors[index]}
                                  </small>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Layout para desktop */}
                        <div className="hidden sm:flex gap-4 w-full">
                          {/* Número de ordem e alça de arrasto */}
                          <div className="flex items-center gap-2">
                            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-700 rounded-full font-bold text-sm flex-shrink-0 self-center">
                              {index + 1}
                            </div>
                            <div
                              {...provided.dragHandleProps}
                              className="drag-handle text-gray-400 hover:text-gray-600"
                              title="Arrastar para reordenar"
                            >
                              <GripVertical className="w-5 h-5" />
                            </div>
                          </div>

                          {/* Preview da imagem */}
                          <div className="relative w-24 h-24 flex-shrink-0 bg-gray-100 rounded-md overflow-hidden">
                            {previewUrls[item.id] ? (
                              <Image
                                src={previewUrls[item.id]}
                                alt={`Preview ${index}`}
                                fill
                                className="object-cover rounded-md"
                                unoptimized
                              />
                            ) : (
                              <div className="flex items-center justify-center h-full w-full">
                                <ImageIcon className="h-8 w-8 text-gray-400" />
                              </div>
                            )}
                          </div>

                          {/* Campo de legenda */}
                          <div className="flex-grow min-w-0">
                            <div className="flex flex-col gap-1.5">
                              <Label
                                htmlFor={`photo-caption-${index}`}
                                className="font-bold text-gray-700 truncate"
                              >
                                Legenda para {item.file.name.length > 20 ? item.file.name.substring(0, 20) + '...' : item.file.name}
                              </Label>
                              <Input
                                id={`photo-caption-${index}`}
                                value={item.description}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleDescriptionChange(index, e.target.value)}
                                placeholder="Digite uma legenda para esta foto"
                                className={errors[index] ? "border-red-500" : ""}
                              />
                              {errors[index] && (
                                <small className="text-xs font-semibold text-destructive">
                                  {errors[index]}
                                </small>
                              )}
                            </div>
                          </div>

                          {/* Botões de ação */}
                          <div className="flex flex-col gap-1 self-start mt-6">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                              onClick={() => handleMoveUp(index)}
                              disabled={index === 0}
                              title="Mover para cima"
                            >
                              <ChevronUp className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-blue-500 hover:bg-blue-50 hover:text-blue-600 h-8 w-8 flex-shrink-0"
                              onClick={() => handleMoveDown(index)}
                              disabled={index === selectedFiles.length - 1}
                              title="Mover para baixo"
                            >
                              <ChevronDown className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-500 hover:bg-red-50 hover:text-red-500 h-8 w-8 flex-shrink-0"
                              onClick={() => handleRemoveFile(index)}
                              title="Remover"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}

                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div className="text-center py-10 bg-white rounded-md border border-dashed border-gray-300 mt-4">
          <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="text-gray-500 mt-2">Nenhuma imagem selecionada</p>
          <p className="text-sm text-gray-400 mt-1">Escolha uma das opções acima para adicionar fotos</p>
        </div>
      )}

      {/* Botões de ação */}
      <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
        <Button
          onClick={onCancel}
          className="bg-blue-500 hover:bg-blue-600 w-full sm:w-auto text-white"
        >
          Cancelar
        </Button>
        <Button
          className="bg-green-500 hover:bg-green-600 w-full sm:w-auto"
          onClick={handleSave}
          disabled={selectedFiles.length === 0}
        >
          Adicionar {selectedFiles.length} foto{selectedFiles.length !== 1 ? 's' : ''}
        </Button>
      </div>
    </div>
  );
}
