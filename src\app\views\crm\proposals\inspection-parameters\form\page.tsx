"use client";
import {
	findInspectionParameter,
	saveInspectionParameter,
	saveLaborEquipamentByInspectionParameter
} from "@/src/actions/inspection-parameters";
import { findProposal } from "@/src/actions/proposals";
import { CustomInput } from "@/src/components/app-input";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/src/components/ui/dialog";
import { toast } from "@/src/hooks/use-toast";
import { parseObject } from "@/src/lib/utils";
import { Photo } from "@/src/types/core/inspection-paramenters";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, GripVertical, Images } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import MultiplePhotoUploadForm from "./components/multiple-photo-upload-form";
import {
	inspectionParameterSchema,
	InspectionParameterSchema,
} from "./schemas/inspection-parameters.schema";
// Importação removida pois não está sendo utilizada
import LaborEquipament from "./components/dialog-labor-equipament";
import CardLaborEquipament from "./components/card-labor-equipament";
import PhotoCard from "./components/photo-card";
import { DragDropContext, Draggable, Droppable, DropResult } from "@hello-pangea/dnd";

// Estilos CSS personalizados
const customStyles = `
  .min-height-200 {
    min-height: 200px;
  }

  .dragging-item {
    pointer-events: auto !important;
    cursor: grabbing !important;
    opacity: 0.9 !important;
    z-index: 9999 !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  }

  .drop-area {
    transition: all 0.2s ease;
    min-height: 200px;
    border-radius: 0.5rem;
    position: relative;
    z-index: 1;
  }

  .drop-area.active {
    background-color: rgba(235, 245, 255, 0.3);
    border: 2px dashed #90cdf4;
    padding: 0.5rem;
    margin: -0.5rem;
  }

  .drag-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
    z-index: 0;
    cursor: grabbing;
  }

  body.dragging {
    cursor: grabbing !important;
    user-select: none !important;
    overflow: hidden !important;
  }
`;

export default function page() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [loading, setLoading] = useState(true);
	const [titleForm, setTitleForm] = useState("Adicionar");
	const [multipleUploadOpen, setMultipleUploadOpen] = useState(false);
	const [photos, setPhotos] = useState<Photo[]>([]);
	// Alterando para usar IDs como chaves em vez de índices
	const [photoPreviewUrls, setPhotoPreviewUrls] = useState<{ [key: string]: string }>({});
	const [proposalId, setProposalId] = useState<string>();
	const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
	const [isLaborOrEquipament, setIsLaborOrEquipament] = useState<string>("");
	const [labor, setLabor] = useState<any[]>([]);
	const [equipaments, setEquipaments] = useState<any[]>([]);
	const [inspectionParameterId, setInspectionParameterId] = useState<string>("");
	const [isDragging, setIsDragging] = useState(false);


	// Adicionar estilos CSS personalizados e eventos de teclado
	useEffect(() => {
		// Criar elemento de estilo
		const styleElement = document.createElement('style');
		styleElement.innerHTML = customStyles;
		document.head.appendChild(styleElement);

		// Adicionar evento de teclado para cancelar o arrasto com a tecla Escape
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key === 'Escape' && isDragging) {
				setIsDragging(false);
			}
		};

		// Adicionar evento de clique para cancelar o arrasto quando clicar fora da área
		const handleClickOutside = () => {
			if (isDragging) {
				setIsDragging(false);
			}
		};

		window.addEventListener('keydown', handleKeyDown);
		window.addEventListener('click', handleClickOutside);

		// Limpar ao desmontar
		return () => {
			document.head.removeChild(styleElement);
			window.removeEventListener('keydown', handleKeyDown);
			window.removeEventListener('click', handleClickOutside);
		};
	}, [isDragging]);

	const methods = useForm<InspectionParameterSchema>({
		resolver: zodResolver(inspectionParameterSchema),
		defaultValues: async () => {
			const formData: InspectionParameterSchema = {
				inspectionDate: new Date(),
				observation: "",
				technicalData: "",
			};

			const id = searchParams.get("id");

			if (id) {
				setTitleForm("Editar");
				setInspectionParameterId(id);
				const inspectionParameter = await findInspectionParameter(id);

				if (inspectionParameter) {
					formData.id = inspectionParameter.id;
					formData.technicalData = inspectionParameter.technicalData;
					formData.observation = inspectionParameter.observation;
					formData.inspectionDate = inspectionParameter.inspectionDate;
					formData.numberInspection = inspectionParameter.numberInspection;

					setPhotos(inspectionParameter.photos!);

					// Carregar mão de obra e equipamentos
					if (inspectionParameter.laborEquipament && inspectionParameter.laborEquipament.length > 0) {
						const laborItems = inspectionParameter.laborEquipament.filter(item => item.labor.type === "LABOR");
						const equipamentItems = inspectionParameter.laborEquipament.filter(item => item.labor.type === "EQUIPAMENT");
						setLabor(laborItems);
						setEquipaments(equipamentItems);
					}
				}
			}

			try {
			} catch (error) {
				console.error(error);
			} finally {
				setLoading(false);
				return formData;
			}
		},
	});

	// Form methods removido pois não está sendo utilizado

	const fetchProposal = async (proposalId: string) => {
		try {
			setLoading(true);
			const data = await findProposal(proposalId);
			if (data) {
				methods.setValue("proposalId", proposalId);
				methods.setValue("proposalName", data.name);
				methods.setValue("customerName", data.customer?.name || "");
			}
		} catch (error) {
			console.log(error);
		} finally {
			setLoading(false);
		}
	};

	const handleSubmit = async () => {
		try {
			// Ativar o loading no início do processo
			setLoading(true);
			console.log('Iniciando processo de salvamento...');

			// Validar todos os campos
			await methods.trigger();
			console.log('Validação de campos concluída');

			// Verificar se o formulário é válido
			const validationResult = inspectionParameterSchema.safeParse(
				methods.getValues()
			);

			if (validationResult.success) {
				const formValues = methods.getValues();

				// Usar diretamente os valores atualizados do estado
				// Não precisamos mais buscar os valores dos inputs, pois eles já estão sincronizados com o estado
				const updatedLabor = labor.map(item => {
					return { ...item };
				});

				const updatedEquipaments = equipaments.map(item => {
					return { ...item };
				});

				console.log('Labor antes de salvar:', updatedLabor);
				console.log('Equipamentos antes de salvar:', updatedEquipaments);

				const laborEquipamentItems = [...updatedLabor, ...updatedEquipaments];
				formValues.laborEquipament = laborEquipamentItems;
				console.log('Itens a serem salvos:', laborEquipamentItems);

				if (photos.length) {
					const fileProcessingPromises = photos
						.filter((photo) => photo.file && !photo.id)
						.map((photo) => {
							return new Promise((resolve, reject) => {
								const reader = new FileReader();

								reader.onload = (e) => {
									const arrayBuffer = e.target?.result;

									if (arrayBuffer) {
										const buffer = Array.from(
											new Uint8Array(arrayBuffer as ArrayBuffer)
										);

										photo.file.buffer = buffer;
										(photo as any).file._name = photo.file.name;
										(photo as any).file._size = photo.file.size;
										(photo as any).file._type = photo.file.type;

										resolve(photo);
									} else {
										reject(new Error("Failed to read file buffer."));
									}
								};

								reader.onerror = () =>
									reject(new Error("FileReader encountered an error."));

								reader.readAsArrayBuffer(photo.file as unknown as File);
							});
						});

					try {
						// Manter o loading ativo durante o processamento das imagens
						await Promise.all(fileProcessingPromises);
						console.log('Processamento de imagens concluído com sucesso');
					} catch (error) {
						console.error('Erro ao processar imagens:', error);
						toast({
							title: "Erro",
							description: "Ocorreu um erro ao processar as imagens. Tente novamente.",
							variant: "destructive"
						});
						setLoading(false);
						return;
					}
				}

				console.log('Iniciando salvamento do parâmetro de inspeção...');
				const data = await saveInspectionParameter(
					parseObject(formValues),
					parseObject(photos)
				);

				if (!data) {
					throw new Error('Falha ao salvar o parâmetro de inspeção');
				}

				console.log('Parâmetro de inspeção salvo com sucesso, ID:', data.id);

				// Salvar mão de obra e equipamentos
				if (laborEquipamentItems.length > 0) {
					console.log('Salvando itens de mão de obra e equipamentos...');
					await saveLaborEquipamentByInspectionParameter(
						data.id,
						laborEquipamentItems
					);
					console.log('Itens de mão de obra e equipamentos salvos com sucesso');
				}

				toast({
					title: "Sucesso",
					description: formValues.id
						? "Parâmetro de inspeção atualizado com sucesso!"
						: "Parâmetro de inspeção criado com sucesso!",
					variant: "default"
				});
				// Manter o loading ativo até a navegação ser concluída
				console.log('Salvamento concluído, iniciando navegação...');

				// Navegar para a página de listagem
				try {
					// Remover o await pois router.push não retorna uma Promise
					router.push(
						`/views/crm/proposals/inspection-parameters?id=${data.proposalId}`
					);
				} catch (error) {
					console.error('Erro durante a navegação:', error);
				} finally {
					// Desativar o loading apenas após a tentativa de navegação
					setLoading(false);
					console.log('Loading desativado após navegação');
				}

				setTitleForm("Editar");
				setPhotos(data.photos || []);
				setInspectionParameterId(data.id);
			} else {
				// Formulário inválido - mostrar mensagem de erro com campos não preenchidos
				const errors = validationResult.error.format();
				const errorFields = Object.keys(errors)
					.filter(key => key !== '_errors' && errors[key]?._errors?.length > 0)
					.map(key => {
						// Mapear nomes de campos para nomes amigáveis
						const fieldNames: Record<string, string> = {
							inspectionDate: 'Data de inspeção',
							observation: 'Comentários',
							technicalData: 'Dados técnicos'
						};
						return fieldNames[key] || key;
					});

				const errorMessage = errorFields.length > 0
					? `Campos obrigatórios não preenchidos: ${errorFields.join(', ')}`
					: 'Formulário inválido. Verifique os campos e tente novamente.';

				toast({
					title: "Erro de validação",
					description: errorMessage,
					variant: "destructive"
				});

				setLoading(false);
			}
		} catch (error) {
			console.error('Erro durante o processo de salvamento:', error);

			// Mensagem de erro mais detalhada
			let errorMessage = "Ocorreu um erro ao salvar o parâmetro de inspeção. Tente novamente.";
			if (error instanceof Error) {
				errorMessage = `${errorMessage} Detalhes: ${error.message}`;
			}

			toast({
				title: "Erro",
				description: errorMessage,
				variant: "destructive"
			});

			// Desativar o loading apenas no final do processo
			setLoading(false);
			console.log('Loading desativado após erro');
		}
	};

	const handleDeletePhoto = (index: number) => {
		// Obter a foto que está sendo removida
		const photoToRemove = photos[index];

		// Obter o ID da foto (id real ou tempId)
		const photoId = photoToRemove.id || (photoToRemove as any).tempId;

		// Filtrar a foto do array
		const newPhotos = photos.filter((_, i) => i !== index);

		// Atualizar o estado das fotos
		setPhotos(newPhotos);

		// Limpar a URL temporária se existir
		if (photoId && photoPreviewUrls[photoId]) {
			// Revogar a URL do objeto
			URL.revokeObjectURL(photoPreviewUrls[photoId]);

			// Criar uma cópia do objeto de URLs
			const newPreviewUrls = { ...photoPreviewUrls };

			// Remover a URL da foto excluída
			delete newPreviewUrls[photoId];

			// Atualizar o estado das URLs
			setPhotoPreviewUrls(newPreviewUrls);
		}
	};

	// Funções removidas pois não estão sendo utilizadas

	const handleMultiplePhotoUpload = (
		selectedFiles: { file: File; description: string }[],
		globalDescription?: string // novo parâmetro opcional
	) => {
		// Para cada arquivo selecionado, criar um objeto Photo e adicionar ao estado
		const newPhotos = selectedFiles.map((item, idx) => {
			// Gerar um ID temporário único para a foto
			const tempId = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}-${idx}`;

			// Se a descrição estiver vazia e globalDescription existir, usar globalDescription
			const description = item.description?.trim()
				? item.description
				: (globalDescription || "");

			// Criar um objeto que satisfaz a interface Photo
			const photo: Photo = {
				description,
				file: item.file as any, // Conversão necessária para compatibilidade
				fileId: "", // Campo obrigatório da interface FileRelationInterface
				id: "", // Campo obrigatório da interface ResourceControlInterface
				order: photos.length + idx, // A ordem será sequencial a partir do último item atual
				tempId // Adicionar ID temporário para rastreamento
			};
			return photo;
		});

		// Adicionar as novas fotos ao estado
		const updatedPhotos = [...photos, ...newPhotos];
		setPhotos(updatedPhotos);

		// Criar URLs de preview para as novas fotos
		const newPreviewUrls: { [key: string]: string } = {};
		newPhotos.forEach((photo) => {
			// Usar o ID ou tempId como chave para a URL de preview
			const photoId = photo.id || (photo as any).tempId;
			if (photo.file instanceof File && photoId) {
				newPreviewUrls[photoId] = URL.createObjectURL(photo.file);
			}
		});

		// Atualizar o estado de previewUrls
		setPhotoPreviewUrls(prev => ({
			...prev,
			...newPreviewUrls
		}));

		// Fechar o diálogo
		setMultipleUploadOpen(false);

		// Mostrar mensagem de sucesso
		toast({
			title: "Sucesso",
			description: `${newPhotos.length} foto${newPhotos.length !== 1 ? 's' : ''} adicionada${newPhotos.length !== 1 ? 's' : ''} com sucesso!`,
			variant: "default"
		});
	};

	// Função para lidar com o início do arrasto
	const handleDragStart = () => {
		// Adicionar classe ao body para evitar scrolling durante o arrasto
		document.body.classList.add('dragging');
		// Definir o estado de arrasto
		setIsDragging(true);
	};

	// Função para lidar com o reordenamento das fotos
	const handleDragEnd = (result: DropResult) => {
		// Remover classe do body
		document.body.classList.remove('dragging');
		// Resetar o estado de arrasto
		setIsDragging(false);

		const { source, destination } = result;

		// Se não houver destino ou se a posição não mudou, não faz nada
		if (!destination) {
			// O usuário soltou o item fora da área de drop
			return;
		}

		if (source.index === destination.index) {
			// A posição não mudou
			return;
		}

		// Se houver apenas uma foto, não faz nada
		if (photos.length <= 1) {
			return;
		}

		// Pequeno timeout para garantir que o estado visual seja atualizado corretamente
		setTimeout(() => {
			// Garantir que o estado de arrasto seja resetado
			setIsDragging(false);
		}, 50);

		// Abordagem simplificada para reordenamento usando IDs
		try {
			// 1. Criar cópia do array de fotos
			const newPhotos = Array.from(photos);

			// 2. Mover a foto da posição original para a nova posição
			const [movedPhoto] = newPhotos.splice(source.index, 1);
			newPhotos.splice(destination.index, 0, movedPhoto);

			// 3. Atualizar a ordem de todas as fotos
			const updatedPhotos = newPhotos.map((photo, index) => ({
				...photo,
				order: index
			}));

			// 4. Atualizar o estado das fotos
			setPhotos(updatedPhotos);

			// Não precisamos atualizar as URLs de preview, pois elas são indexadas pelo ID da foto
			// e não pela posição no array, então elas permanecem válidas mesmo após a reordenação

		} catch (error) {
			console.error("Erro ao reordenar fotos:", error);

			// Método de recuperação de emergência
			// Simplesmente reordenar as fotos sem tentar manter as URLs de preview
			const newPhotos = Array.from(photos);
			const [movedPhoto] = newPhotos.splice(source.index, 1);
			newPhotos.splice(destination.index, 0, movedPhoto);

			const updatedPhotos = newPhotos.map((photo, index) => ({
				...photo,
				order: index
			}));

			// Atualizar apenas o estado das fotos
			setPhotos(updatedPhotos);

			// Mostrar mensagem de erro para o usuário
			toast({
				title: "Aviso",
				description: "Houve um problema ao reordenar as imagens. Algumas previsualizações podem não ser exibidas corretamente.",
				variant: "destructive"
			});
		}
	};

	// Limpar URLs de objeto quando o componente for desmontado
	useEffect(() => {
		return () => {
			// Limpar todas as URLs de objeto ao desmontar o componente
			Object.values(photoPreviewUrls).forEach(url => {
				URL.revokeObjectURL(url);
			});
		};
	}, [photoPreviewUrls]);

	useEffect(() => {
		const proposalId = searchParams.get("proposalId");
		if (proposalId) {
			fetchProposal(proposalId);
			setProposalId(proposalId);
		}
	}, [searchParams.get("proposalId")]);

	// Funções para lidar com mão de obra e equipamentos
	const handleAddLaborClick = (type: string) => {
		setIsLaborOrEquipament(type);
		setIsDialogOpen(true);
	};

	const handleRemoveItem = async (id: string) => {
		try {
			setLoading(true);
			// Remover do estado local
			const newLabor = labor.filter(item => item.laborId !== id);
			const newEquipaments = equipaments.filter(item => item.laborId !== id);
			setLabor(newLabor);
			setEquipaments(newEquipaments);

			// Não exibir toast aqui, apenas quando o usuário clicar em Salvar/Atualizar
		} catch (error) {
			console.error("Erro ao remover item:", error);
			toast({
				title: "Erro",
				description: "Não foi possível remover o item. Tente novamente.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	// Função para atualizar a quantidade de um item
	const handleAmountChange = (id: string, amount: number) => {
		// Verificar se o item é mão de obra ou equipamento
		const laborIndex = labor.findIndex(item => item.laborId === id);
		if (laborIndex >= 0) {
			// Atualizar a quantidade do item de mão de obra
			const updatedLabor = [...labor];
			updatedLabor[laborIndex].amount = amount;
			setLabor(updatedLabor);
			console.log(`Quantidade de mão de obra atualizada: ${id} = ${amount}`);
			return;
		}

		const equipamentIndex = equipaments.findIndex(item => item.laborId === id);
		if (equipamentIndex >= 0) {
			// Atualizar a quantidade do item de equipamento
			const updatedEquipaments = [...equipaments];
			updatedEquipaments[equipamentIndex].amount = amount;
			setEquipaments(updatedEquipaments);
			console.log(`Quantidade de equipamento atualizada: ${id} = ${amount}`);
		}
	};

	const setSelectedLabors = async (selectedLabors: any[]) => {
		try {
			setLoading(true);
			// Filtrar apenas itens válidos (com labor e labor.name definidos)
			const validSelectedLabors = selectedLabors.filter(item => item.labor && item.labor.name);

			// Atualizar o estado local
			const updatedLabor = [...labor];
			const updatedEquipaments = [...equipaments];

			if (isLaborOrEquipament === "LABOR") {
				// Para cada item selecionado, verificar se já existe
				validSelectedLabors.forEach(newItem => {
					const existingItemIndex = updatedLabor.findIndex(item => item.laborId === newItem.laborId);

					if (existingItemIndex >= 0) {
						// Se o item já existe, incrementar a quantidade
						// Usar o valor atual do estado
						const currentAmount = Number(updatedLabor[existingItemIndex].amount) || 1;

						// Incrementar a quantidade
						updatedLabor[existingItemIndex].amount = Number(currentAmount) + 1;
						console.log(`Incrementando item ${updatedLabor[existingItemIndex].labor.name} para quantidade ${updatedLabor[existingItemIndex].amount}`);
					} else {
						// Se o item não existe, adicionar à lista
						updatedLabor.push(newItem);
					}
				});
				setLabor(updatedLabor);
			} else {
				// Para cada item selecionado, verificar se já existe
				validSelectedLabors.forEach(newItem => {
					const existingItemIndex = updatedEquipaments.findIndex(item => item.laborId === newItem.laborId);

					if (existingItemIndex >= 0) {
						// Se o item já existe, incrementar a quantidade
						// Usar o valor atual do estado
						const currentAmount = Number(updatedEquipaments[existingItemIndex].amount) || 1;

						// Incrementar a quantidade
						updatedEquipaments[existingItemIndex].amount = Number(currentAmount) + 1;
						console.log(`Incrementando item ${updatedEquipaments[existingItemIndex].labor.name} para quantidade ${updatedEquipaments[existingItemIndex].amount}`);
					} else {
						// Se o item não existe, adicionar à lista
						updatedEquipaments.push(newItem);
					}
				});
				setEquipaments(updatedEquipaments);
			}

			// Não exibir toast aqui, apenas quando o usuário clicar em Salvar/Atualizar
		} catch (error) {
			console.error("Erro ao adicionar itens:", error);
			toast({
				title: "Erro",
				description: "Não foi possível adicionar os itens. Tente novamente.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	};

	return (
		<ContentWrapper
			title={`${titleForm} parâmetro de inspeção`}
			loading={loading}
		>
			<FormProvider {...methods}>
				<form className="flex flex-col gap-3 mt-4">
					<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
						<CustomInput name="proposalName" label="Proposta" disabled={true} />
						<CustomInput name="customerName" label="Cliente" disabled={true} />
						<CustomInput
							label="Data de inspeção"
							name="inspectionDate"
							type="date"
							className="w-full"
							placeholder="Data de inspeção"
							hideErrorMessage={true}
						/>
					</div>
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<CustomInput
							label="Comentários"
							type="textarea"
							name="observation"
							placeholder="Comentários"
							hideErrorMessage={true}
						/>
						<CustomInput
							label="Dados técnicos"
							type="textarea"
							name="technicalData"
							placeholder="Dados técnicos"
							hideErrorMessage={true}
						/>
					</div>

					{/* Seção de Mão de Obra e Equipamentos */}
					<div className="border-t border-gray-200 pt-6 mt-6">
						<div className="flex flex-col space-y-6">
							{/* Seção de Mão de Obra */}
							<div className="bg-gray-50 rounded-lg p-4">
								<div className="flex items-center justify-between mb-4">
									<h3 className="text-lg font-medium flex items-center gap-2">
										Mão de Obra
									</h3>
									<Button
										className="bg-blue-500 hover:bg-blue-600"
										size="sm"
										type="button"
										onClick={() => handleAddLaborClick("LABOR")}
									>
										Adicionar
										<Plus className="ml-1 h-4 w-4" />
									</Button>
								</div>

								{/* Lista de itens */}
								{labor?.length > 0 ? (
									<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
										{labor.filter(item => item.labor && item.labor.name).map((item, index) => (
											<CardLaborEquipament
												key={index}
												id={item.laborId}
												nameInput={`${item.laborId}`}
												title={item.labor.name}
												description={item.labor.description || ""}
												removeItem={handleRemoveItem}
												type="LABOR"
												amount={item.amount || 1}
												onAmountChange={handleAmountChange}
											/>
										))}
									</div>
								) : (
									<div className="text-center py-6 bg-white rounded-md border border-dashed border-gray-300">
										<p className="text-gray-500">Nenhuma mão de obra adicionada</p>
									</div>
								)}
							</div>

							{/* Seção de Equipamentos */}
							<div className="bg-gray-50 rounded-lg p-4">
								<div className="flex items-center justify-between mb-4">
									<h3 className="text-lg font-medium flex items-center gap-2">
										Equipamentos
									</h3>
									<Button
										className="bg-blue-500 hover:bg-blue-600"
										size="sm"
										type="button"
										onClick={() => handleAddLaborClick("EQUIPAMENT")}
									>
										Adicionar
										<Plus className="ml-1 h-4 w-4" />
									</Button>
								</div>

								{/* Lista de itens */}
								{equipaments?.length > 0 ? (
									<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
										{equipaments.filter(item => item.labor && item.labor.name).map((item, index) => (
											<CardLaborEquipament
												key={index}
												id={item.laborId}
												nameInput={`${item.laborId}`}
												title={item.labor.name}
												description={item.labor.description || ""}
												removeItem={handleRemoveItem}
												type="EQUIPAMENT"
												amount={item.amount || 1}
												onAmountChange={handleAmountChange}
											/>
										))}
									</div>
								) : (
									<div className="text-center py-6 bg-white rounded-md border border-dashed border-gray-300">
										<p className="text-gray-500">Nenhum equipamento adicionado</p>
									</div>
								)}
							</div>
						</div>

						{/* Componente de diálogo compartilhado */}
						<LaborEquipament
							isOpen={isDialogOpen}
							onOpenChange={setIsDialogOpen}
							type={isLaborOrEquipament}
							setSelectedLabors={setSelectedLabors}
						/>
					</div>

					{/* Seção de Fotos */}
					<div className="border-t border-gray-200 pt-6 mt-6">
						<div className="bg-gray-50 rounded-lg p-4">
							<div className="flex items-center justify-between mb-4">
								<div>
									<h3 className="text-lg font-medium flex items-center gap-2">
										Fotos da Inspeção
									</h3>
									{photos.length > 1 && (
										<p className="text-xs text-gray-500 mt-1 flex items-center">
											<GripVertical className="w-3 h-3 mr-1" />
											Arraste e solte para reordenar as fotos
										</p>
									)}
								</div>

								<div className="flex gap-2">
									{/* <Dialog open={open} onOpenChange={handlePhotoDialogOpen}>
										<DialogTrigger asChild>
											 <Button
												className="bg-blue-500 hover:bg-blue-600"
												size="sm"
												type="button"
											>
												Adicionar
												<Plus className="ml-1 h-4 w-4" />
											</Button>
										</DialogTrigger>
										<DialogContent className="sm:max-w-[425px]">
											<DialogHeader>
												<DialogTitle className="flex items-center gap-2">
													<span>Adicionar foto</span>
												</DialogTitle>
												<DialogDescription />
											</DialogHeader>
											<PhotoUploadForm
												methods={photoUploadFormMethods}
												onFileInputChange={(file) =>
													file && photoUploadFormMethods.setValue("file", file)
												}
											/>

											<DialogFooter className="flex flex-col gap-3 mt-4">
												<DialogClose asChild>
													<Button
														type="button"
														variant="outline"
													>
														Cancelar
													</Button>
												</DialogClose>
												<Button
													type="button"
													className="bg-green-500 hover:bg-green-600"
													onClick={() =>
														photoUploadFormMethods.handleSubmit(handlePhotoUpload)()
													}
												>
													Salvar
												</Button>
											</DialogFooter>
										</DialogContent>
									</Dialog> */}

									<Dialog open={multipleUploadOpen} onOpenChange={setMultipleUploadOpen}>
										<DialogTrigger asChild>
											<Button
												className="bg-blue-500 hover:bg-blue-600"
												size="sm"
												type="button"
											>
												Adicionar imagens
												<Images className="ml-1 h-4 w-4" />
											</Button>
										</DialogTrigger>
										<DialogContent className="sm:max-w-[600px] w-[95vw] max-w-full">
											<DialogHeader>
												<DialogTitle className="flex items-center gap-2">
													<span>Adicionar imagens</span>
												</DialogTitle>
												<DialogDescription>
													Selecione uma ou mais imagens e adicione legendas para cada uma delas
												</DialogDescription>
											</DialogHeader>
											<MultiplePhotoUploadForm
												onSave={(files, globalDescription) => handleMultiplePhotoUpload(files, globalDescription)}
												onCancel={() => setMultipleUploadOpen(false)}
											/>
										</DialogContent>
									</Dialog>
								</div>
							</div>

							{/* Overlay para capturar cliques durante o arrasto */}
							{isDragging && <div className="drag-overlay" onClick={() => setIsDragging(false)}></div>}

							{/* Lista de fotos */}
							{photos?.length > 0 ? (
								<DragDropContext
									onDragStart={handleDragStart}
									onDragEnd={handleDragEnd}
								>
									<Droppable
										droppableId="photos"
										direction="horizontal"
										isDropDisabled={photos.length <= 1} // Desabilitar o drop quando há apenas um item
									>
										{(provided, snapshot) => (
											<div
												{...provided.droppableProps}
												ref={provided.innerRef}
												className={`
													grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3
													drop-area ${snapshot.isDraggingOver ? 'active' : ''}
													${isDragging ? 'min-height-200 relative' : ''}
												`}
											>
												{photos
													.sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
													.map((photo, index) => (
														<Draggable
															key={photo.id || (photo as any).tempId || `photo-${index}`}
															draggableId={photo.id || (photo as any).tempId || `photo-${index}`}
															index={index}
															isDragDisabled={photos.length <= 1} // Desabilitar o drag quando há apenas um item
														>
															{(provided, snapshot) => (
																<div
																	ref={provided.innerRef}
																	{...provided.draggableProps}
																	{...provided.dragHandleProps}
																	className={`
																		${snapshot.isDragging ? 'dragging-item' : ''}
																		transition-all duration-200
																	`}
																	style={{
																		...provided.draggableProps.style,
																		// Aplicar transformação apenas quando estiver arrastando e houver mais de um item
																		transform: snapshot.isDragging && photos.length > 1
																			? `${provided.draggableProps.style?.transform} rotate(2deg) scale(1.01)`
																			: provided.draggableProps.style?.transform,
																		// Garantir que o item seja visível apenas durante o arrasto
																		opacity: snapshot.isDragging ? 0.9 : 1,
																		// Adicionar um z-index alto para garantir que o item fique acima de outros elementos
																		zIndex: snapshot.isDragging ? 9999 : 'auto',
																	}}
																	// Adicionar evento de clique para garantir que o arrasto seja cancelado
																	onClick={() => {
																		if (isDragging) {
																			setIsDragging(false);
																		}
																	}}
																>
																	<PhotoCard
																		photo={photo}
																		index={index}
																		photoPreviewUrl={photoPreviewUrls[photo.id || (photo as any).tempId]}
																		onDelete={handleDeletePhoto}
																		isDraggable={photos.length > 1} // Passar prop para indicar se é arrastável
																	/>
																</div>
															)}
														</Draggable>
													))}
												{provided.placeholder}
											</div>
										)}
									</Droppable>
								</DragDropContext>
							) : (
								<div className="text-center py-10 bg-white rounded-md border border-dashed border-gray-300">
									<p className="text-gray-500">Nenhuma foto adicionada</p>
									<p className="text-sm text-gray-400 mt-1">Clique em &ldquo;Adicionar&rdquo; para incluir fotos da inspeção</p>
								</div>
							)}
						</div>
					</div>
					<div className="flex justify-end gap-4">
						<Button
							className="border-blue-500 text-blue-500 hover:text-blue-400 hover:border-blue-400"
							variant="outline"
							type="button"
							onClick={() => router.push(
								`/views/crm/proposals/inspection-parameters?id=${proposalId}`
							)}
						>
							Voltar
						</Button>
						<Button
							className="bg-green-500 hover:bg-green-600"
							type="button"
							onClick={() => handleSubmit()}
							disabled={loading}
						>
							{inspectionParameterId ? "Atualizar" : "Salvar"}
						</Button>
					</div>
				</form>
			</FormProvider>
		</ContentWrapper>
	);
}
