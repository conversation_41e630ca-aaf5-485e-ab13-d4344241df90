"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/src/components/ui/sheet";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import { Eye } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
// import ProposalForm from "../_components/proposal-form";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations } from "@/src/constants";
import { ServicesScope } from "@/src/types/core/services-scope";
import { loadProposalTemplates } from "@/src/actions/proposal-templates";
// import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import ContractsLostTable, { ContractsLostTableRef } from "./_components/contracts-lost-table";

export default function LostContracts() {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [customers, setCustomers] = useState<Customer[]>([]);
    // const [templates, setTemplates] = useState<ProposalTemplateInterface[]>([]);
    const [sheetOpen, setSheetOpen] = useState(false);
    // const [proposal, setProposal] = useState<Proposal | undefined>(undefined);
    // const [serviceScopes, setServiceScopes] = useState<ServicesScope[]>([]);
    // const [currentPage, setCurrentPage] = useState(1);
    const tableRef = useRef<ContractsLostTableRef>(null);

    // const handleProposalSheet = async (proposal?: Proposal) => {
    //     setProposal(proposal);
    //     setSheetOpen(true);
    // };

    const fetchCustomers = async () => {
        try {
            const res = await fetch("/api/customers?pageSize=1000");
            if (!res.ok) throw new Error("Erro ao carregar clientes");

            const result = await res.json();
            console.log("Dados de clientes recebidos:", result);

            // Verificar se os dados estão na propriedade 'data' ou diretamente no resultado
            const customers = result.data || result.items || result;

            // Garantir que customers é um array
            if (!Array.isArray(customers)) {
                console.error("Dados de clientes não são um array:", customers);
                return [];
            }

            return customers as Customer[];
        } catch (error) {
            console.error("Erro ao carregar clientes:", error);
            return [];
        }
    };

    const fetchTemplates = async () => {
        try {
            const data = await loadProposalTemplates(false);
            return data as any
        } catch (error) {
            console.error(error);
        }
    };

    const fetchServiceScopes = async () => {
        try {
            const params = new URLSearchParams();
            ["PROPOSAL_SERVICE"].forEach((type) => params.append("type", type));

            const res = await fetch(`/api/service-scopes?${params.toString()}`);
            if (!res.ok) throw new Error("Erro ao carregar escopo de serviços");

            const data = await res.json();
            return data as ServicesScope[];
        } catch (error) {
            console.error(error);
        }
    };



    async function loadData() {
        setLoading(true);
        try {
            const [customersData] = await Promise.all([
                fetchCustomers(),
                fetchTemplates(),
                fetchServiceScopes(),
            ]);

            setCustomers(customersData || []);
            // setTemplates(templatesData || []);
            // setServiceScopes(scopesData || []);
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        loadData();
    }, []);

    const columns: Column<Proposal>[] = [
        {
            key: "customer",
            header: "Cliente",
            cell: (row) => (row.customer as Customer).name,
            sortable: true,
            filterable: true,
            filterOptions: Array.isArray(customers) ? customers.map((c) => ({
                label: c.name,
                value: c.id,
            })) : [],
            getFilterValue: (row) => (row.customer as Customer).id,
        },
        {
            key: "name",
            header: "Projeto",
            sortable: true,
        },
        {
            key: "budget",
            header: "Orçamento",
            cell: (row) => formatCurrency(row.budget),
            sortable: true,
        },
        {
            key: "startDate",
            header: "Data início",
            cell: (row) => formatDate(row.startDate, "DATE"),
            sortable: true,
        },
        {
            key: "endDate",
            header: "Data de conclusão prevista",
            cell: (row) => formatDate(row.endDate, "DATE"),
            sortable: true,
        },
        {
            key: "situation",
            header: "Situação",
            cell: (row) => {
                const rowValue = row.situation as ProposalSituation;
                const situationLabel = proposalSituations.find(
                    (situation) => situation.value == rowValue
                )?.label || '';

                return (
                    <span className="status-badge status-badge-lost">
                        {situationLabel}
                    </span>
                );
            },
            sortable: true,
        },
        {
            key: "actions",
            header: "Ações",
            cell: (row) => (
                <div className="flex gap-3">
                    <Eye
                        className="size-5 text-green-500 cursor-pointer"
                        onClick={() => router.push(`/views/crm/proposals/completed/${row.id}?from=lost`)}
                    />
                </div>
            ),
        },
    ];

    return (
        <ContentWrapper title="Contratos perdidos" loading={loading}>
            <ContractsLostTable
                ref={tableRef}
                columns={columns}
            // onPageChange={(page) => setCurrentPage(page)}
            />
            <Sheet
                open={sheetOpen}
                onOpenChange={(val) => !val && setSheetOpen(false)}
            >
                <SheetContent className="w-[90%] sm:min-w-[450px] ">
                    <SheetHeader>
                        <SheetTitle />
                        <SheetDescription />
                    </SheetHeader>
                    {/* <ProposalForm
                        scopes={serviceScopes}
                        proposal={proposal}
                        templates={templates}
                        customers={customers}
                        isContractValidated
                        onChange={() => tableRef.current?.refresh(currentPage)}
                        onCancelClick={() => setSheetOpen(false)}
                    /> */}
                </SheetContent>
            </Sheet>
        </ContentWrapper>
    );
}
