"use client";
import { useEffect, useRef, useState } from "react";
import ContractsToStartTable, { ContractsToStartTableRef } from "./_components/contracts-to-start-table";
import { useRouter } from "next/navigation";
import ContentWrapper from "@/src/components/content-wrapper";
import { formatCurrency, formatDate } from "@/src/lib/utils";
import { useToast } from "@/src/hooks/use-toast";
import { Customer } from "@/src/types/core/customer";
import { Proposal, ProposalSituation } from "@/src/types/core/proposal";
import {
  CircleDollarSign,

  Eye,
  SquarePen,
} from "lucide-react";
import { Column } from "@/src/components/ui/table-grid";
import { proposalSituations } from "@/src/constants";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/src/components/ui/sheet";
import ProposalForm from "../_components/proposal-form";
import { ProposalTemplateInterface } from "@/src/types/core/proposal-template";
import { ServicesScope } from "@/src/types/core/services-scope";

export default function ContractsToStart() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [formLoading, setFormLoading] = useState(false);
  const [editingProposalId, setEditingProposalId] = useState<string | null>(null);
  // const [isNavigating, setIsNavigating] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [templates, setTemplates] = useState<ProposalTemplateInterface[]>([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [proposal, setProposal] = useState<Proposal | undefined>(undefined);
  const [serviceScopes, setServiceScopes] = useState<ServicesScope[]>([]);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentSearch, setCurrentSearch] = useState("");
  const [currentSituationFilter, setCurrentSituationFilter] = useState<string | null>(null);
  const [skipInitialLoad, setSkipInitialLoad] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const tableRef = useRef<ContractsToStartTableRef>(null);

  // Função para salvar o estado antes da exclusão
  // const savePreDeleteState = () => {
  //   let stateToSave = {
  //     page: 1,
  //     situationFilter: null as string | null,
  //     search: ""
  //   };

  //   if (tableRef.current) {
  //     const tableState = tableRef.current.getTableState();
  //     stateToSave = {
  //       page: tableState.page,
  //       situationFilter: tableState.situationFilter,
  //       search: tableState.search
  //     };
  //     console.log("Estado obtido da tabela antes da exclusão:", stateToSave);
  //   }

  //   return stateToSave;
  // };

  // Função para excluir um contrato
  // const deleteContract = async (id: string) => {
  //   try {
  //     // Obter o estado atual da tabela
  //     const savedState = savePreDeleteState();
  //     console.log("Estado obtido da tabela antes da exclusão:", savedState);

  //     // A flag de exclusão já foi definida quando o diálogo foi aberto
  //     // Verificar se a flag está definida
  //     if (typeof window !== 'undefined') {
  //       const isDeleting = localStorage.getItem('isContractDeleting');
  //       if (!isDeleting) {
  //         console.log("Flag de exclusão não encontrada, definindo como true");
  //         localStorage.setItem('isContractDeleting', 'true');
  //       } else {
  //         console.log("Flag de exclusão já está definida como:", isDeleting);
  //       }
  //     }

  //     setLoading(true);
  //     const data = await removeProposal(id);

  //     if (data) {
  //       toast({
  //         title: "Sucesso",
  //         description: "Contrato excluído com sucesso",
  //       });

  //       // Atualizar a tabela com a URL da API salva no localStorage
  //       if (tableRef.current) {
  //         // Verificar se há uma URL da API salva no localStorage
  //         if (typeof window !== 'undefined') {
  //           const savedApiUrl = localStorage.getItem('lastContractApiUrl');
  //           if (savedApiUrl) {
  //             console.log("URL da API obtida do localStorage após exclusão:", savedApiUrl);
  //             // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
  //             tableRef.current.refreshWithApiUrl(savedApiUrl);

  //             // Limpar a flag de exclusão após a exclusão
  //             localStorage.removeItem('isContractDeleting');
  //             console.log("Flag de exclusão removida após a exclusão");

  //             return;
  //           }
  //         }

  //         // Fallback: usar os valores do estado salvo
  //         console.log("URL da API não encontrada no localStorage, usando valores do estado salvo");
  //         tableRef.current.refresh(
  //           savedState.page,
  //           savedState.search,
  //           savedState.situationFilter
  //         );

  //         // Limpar a flag de exclusão mesmo quando não há URL da API salva
  //         localStorage.removeItem('isContractDeleting');
  //         console.log("Flag de exclusão removida (fallback sem URL da API)");
  //       } else {
  //         // Fallback: usar os valores do estado salvo
  //         tableRef.current.refresh(
  //           savedState.page,
  //           savedState.search,
  //           savedState.situationFilter
  //         );

  //         // Limpar a flag de exclusão mesmo quando não há referência à tabela
  //         if (typeof window !== 'undefined') {
  //           localStorage.removeItem('isContractDeleting');
  //           console.log("Flag de exclusão removida (fallback sem referência à tabela)");
  //         }
  //       }
  //     } else {
  //       // Limpar a flag de exclusão quando não há dados retornados
  //       if (typeof window !== 'undefined') {
  //         localStorage.removeItem('isContractDeleting');
  //         console.log("Flag de exclusão removida (sem dados retornados)");
  //       }

  //       setLoading(false);
  //     }
  //   } catch (error) {
  //     console.error(error);
  //     toast({
  //       title: "Erro",
  //       description: "Erro ao excluir contrato",
  //       variant: "destructive"
  //     });

  //     // Limpar a flag de exclusão em caso de erro
  //     if (typeof window !== 'undefined') {
  //       localStorage.removeItem('isContractDeleting');
  //       console.log("Flag de exclusão removida (erro na exclusão)");
  //     }

  //     setLoading(false);
  //   }
  // };

  const fetchCustomers = async () => {
    try {
      const res = await fetch("/api/customers?pageSize=1000");
      const data = await res.json();
      return data?.data || data?.items || data || [];
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  const fetchTemplates = async () => {
    try {
      const res = await fetch("/api/templates?pageSize=1000");
      const data = await res.json();
      return data?.data || data?.items || data || [];
    } catch (error) {
      console.error(error);
      return [];
    }
  };

  const fetchServiceScopes = async () => {
    try {
      const res = await fetch("/api/service-scopes?pageSize=1000");
      const data = await res.json();
      return data?.data || data?.items || data || [];
    } catch (error) {
      console.error(error);
      return [];
    }
  };



  // const handleNavigation = (url: string) => {
  //   setIsNavigating(true);
  //   router.push(url);
  // };



  const handleProposalSheet = async (proposal?: Proposal) => {
    try {
      // Limpar o estado da proposta antes de qualquer operação
      // Isso garante que o formulário seja limpo ao abrir para cadastro
      setProposal(undefined);

      // Definir o ID da proposta que está sendo editada (apenas se for edição)
      if (proposal) {
        setEditingProposalId(proposal.id);
      } else {
        // Se for um novo cadastro, garantir que o ID seja limpo
        setEditingProposalId(null);
      }

      // Definir a flag skipInitialLoad como true para evitar que o useEffect carregue a página 1
      setSkipInitialLoad(true);

      // Definir isInitialLoad como false para evitar que a tabela seja carregada na página 1
      setIsInitialLoad(false);

      // Guardar o estado atual da tabela (página e filtros) antes de qualquer operação
      if (tableRef.current) {
        // Obter o estado atual da tabela
        const tableState = tableRef.current.getTableState();
        setCurrentPage(tableState.page);
        setCurrentSearch(tableState.search);
        setCurrentSituationFilter(tableState.situationFilter);

        console.log("Estado obtido da tabela:", { page: tableState.page, search: tableState.search, situationFilter: tableState.situationFilter });
      }

      // Carregar os dados necessários para o formulário de edição
      setFormLoading(true);
      try {
        const [scopesData, templatesData, customersData] = await Promise.all([
          fetchServiceScopes(),
          fetchTemplates(),
          fetchCustomers(),
        ]);
        setServiceScopes(scopesData);
        setTemplates(templatesData);
        setCustomers(customersData);

        // Só definir a proposta se for edição
        if (proposal) {
          setProposal(proposal);
        }

        // Abrir o formulário depois de carregar os dados
        setSheetOpen(true);
      } catch (error) {
        console.error("Erro ao carregar dados para o formulário:", error);
        toast({
          title: "Erro",
          description: "Erro ao carregar dados para o formulário",
          variant: "destructive"
        });
      } finally {
        setFormLoading(false);
      }
    } catch (error) {
      console.error("Erro ao carregar dados para o formulário:", error);
      // Limpar o ID da proposta em caso de erro
      setEditingProposalId(null);
    }
  };

  // Usando função normal em vez de useCallback para evitar problemas
  async function loadData() {
    setLoading(true);
    try {
      // Carregar clientes e propostas em paralelo
      const [customersData, proposalsData] = await Promise.all([
        fetchCustomers(),
        fetch("/api/proposals?situation=SIGN_REQUESTED&situation=SIGNED&pageSize=1000").then(res => res.json())
      ]);

      setCustomers(customersData || []);
      setProposals(proposalsData?.data || []);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar dados",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setIsInitialLoad(false);
    }
  }

  // Carregar dados quando o componente for montado
  useEffect(() => {
    // Limpar a URL da API salva no localStorage quando o componente é montado
    if (typeof window !== 'undefined' && isInitialLoad) {
      localStorage.removeItem('lastContractApiUrl');
      console.log("URL da API removida do localStorage no carregamento inicial");
    }

    loadData();

    // Limpar a URL da API salva no localStorage quando o componente é desmontado
    return () => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('lastContractApiUrl');
        console.log("URL da API removida do localStorage ao desmontar o componente");
      }
    };
  }, [isInitialLoad]);

  const columns: Column<Proposal>[] = [
    {
      key: "customer",
      header: "Cliente",
      cell: (row) => (row.customer as Customer)?.name,
      sortable: true,
    },
    {
      key: "name",
      header: "Projeto",
      cell: (row) => row.name,
      sortable: true,
    },
    {
      key: "budget",
      header: "Orçamento",
      cell: (row) => (
        <div className="flex items-center gap-1">
          <CircleDollarSign className="w-4 h-4 text-green-500" />
          {formatCurrency(row.budget)}
        </div>
      ),
      sortable: true,
    },
    {
      key: "startDate",
      header: "Data de início",
      cell: (row) => formatDate(row.startDate),
      sortable: true,
    },
    {
      key: "endDate",
      header: "Data de conclusão prevista",
      cell: (row) => formatDate(row.endDate),
      sortable: true,
    },
    {
      key: "situation",
      header: "Situação",
      cell: (row) => {
        const rowValue = row.situation as ProposalSituation;
        const situationLabel = proposalSituations.find(
          (situation) => situation.value == rowValue
        )?.label || '';

        // Determinar a classe do badge com base na situação
        let badgeClass = '';

        switch (rowValue) {
          case 'SIGN_REQUESTED':
            badgeClass = 'status-badge-sign';
            break;
          case 'SIGNED':
            badgeClass = 'status-badge-signed';
            break;
          default:
            badgeClass = '';
        }

        return (
          <span className={`status-badge ${badgeClass}`}>
            {situationLabel}
          </span>
        );
      },
      sortable: true,
      filterable: true,
      filterOptions: [
        {
          label: "Solicitação de assinatura",
          value: "SIGN_REQUESTED",
        },
        {
          label: "Assinado",
          value: "SIGNED",
        },
      ],
      getFilterValue: (row) => row.situation as string,
      filterType: 'server', // Indica que o filtro deve ser processado no servidor
    },
    {
      key: "actions",
      header: "Ações",
      cell: (row) => {
        const status = row.situation as string;

        return (
          <div className="flex gap-3">
            {/* Ícone de visualização (sempre visível) */}
            <Eye
              className="size-5 text-green-500 cursor-pointer"
              onClick={(e) => {
                // Impedir que o evento se propague para a tabela
                e.stopPropagation();
                // Navegar para a página de detalhes
                router.push(`/views/crm/proposals/completed/${row.id}?from=to-start`);
              }}
            />

            {/* Ícone de edição (visível apenas para status "SIGN_REQUESTED") */}
            {status === "SIGN_REQUESTED" && (
              editingProposalId === row.id ? (
                <div className="size-5 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                </div>
              ) : (
                <SquarePen
                  className="size-5 text-green-500 cursor-pointer"
                  onClick={(e) => {
                    // Impedir que o evento se propague para a tabela
                    e.stopPropagation();
                    // Chamar a função handleProposalSheet
                    handleProposalSheet(row);
                  }}
                />
              )
            )}

            {/*
            // Código do ícone de exclusão removido, mas a lógica foi mantida para uso futuro
            // Para adicionar o botão de exclusão novamente, descomente o código abaixo:

            <AppConfirmationDialog
              title="Excluir contrato"
              description={`Tem certeza que deseja excluir o contrato ${row.name}?`}
              onConfirm={() => deleteContract(row.id)}
              onOpenChange={(open) => {
                // Quando o diálogo for aberto, salvar o estado atual da tabela
                if (open) {
                  // Obter o estado atual da tabela
                  const savedState = savePreDeleteState();
                  console.log("Estado obtido da tabela ao abrir o diálogo:", savedState);

                  // Salvar os valores no localStorage imediatamente
                  if (typeof window !== 'undefined') {
                    // Definir a flag de exclusão quando o diálogo é aberto
                    localStorage.setItem('isContractDeleting', 'true');
                    console.log("Flag de exclusão definida como true (diálogo aberto)");

                    // Verificar se há uma URL da API salva no localStorage
                    const savedApiUrl = localStorage.getItem('lastContractApiUrl');

                    if (savedApiUrl) {
                      console.log("URL da API obtida do localStorage ao abrir o diálogo:", savedApiUrl);
                      try {
                        // Extrair parâmetros da URL da API para debug
                        const url = new URL(savedApiUrl.startsWith('http') ? savedApiUrl : `${window.location.origin}${savedApiUrl}`);
                        const pageParam = url.searchParams.get('page');
                        const pageSizeParam = url.searchParams.get('pageSize');
                        const situationParams = url.searchParams.getAll('situation');
                        const searchParam = url.searchParams.get('search');

                        console.log("Parâmetros extraídos da URL da API ao abrir o diálogo:", {
                          page: pageParam,
                          pageSize: pageSizeParam,
                          situations: situationParams,
                          search: searchParam
                        });
                      } catch (error) {
                        console.error("Erro ao extrair parâmetros da URL da API:", error);
                      }
                    } else {
                      console.log("Nenhuma URL da API encontrada, usando valores do estado atual");
                    }

                    console.log("Diálogo aberto: URL da API salva no localStorage:", localStorage.getItem('lastContractApiUrl'));
                  }
                } else {
                  // Quando o diálogo for fechado (sem confirmar a exclusão), manter a página atual
                  // Isso é necessário porque o fechamento do diálogo pode causar uma re-renderização
                  // que faz com que a tabela volte para a página 1

                  // Limpar a flag de exclusão quando o diálogo é fechado sem confirmar
                  if (typeof window !== 'undefined') {
                    localStorage.removeItem('isContractDeleting');
                    console.log("Flag de exclusão removida (diálogo fechado sem confirmar)");
                  }

                  setTimeout(() => {
                    if (tableRef.current) {
                      // Verificar se há uma URL da API salva no localStorage
                      if (typeof window !== 'undefined') {
                        const savedApiUrl = localStorage.getItem('lastContractApiUrl');
                        if (savedApiUrl) {
                          console.log("Dialog fechado: URL da API obtida do localStorage:", savedApiUrl);
                          // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
                          tableRef.current.refreshWithApiUrl(savedApiUrl);
                          return;
                        }
                      }

                      // Se não conseguir obter os parâmetros da URL da API, usar o estado atual da tabela
                      const tableState = tableRef.current.getTableState();
                      tableRef.current.refresh(tableState.page, tableState.search, tableState.situationFilter);
                    }
                  }, 100);
                }
              }}
              dialogCancelClassName="bg-transparent hover:bg-background/80"
              dialogActionClassName="bg-destructive hover:bg-destructive/90"
            >
              <Trash className="size-5 cursor-pointer" color="#ef4444" />
            </AppConfirmationDialog>
            */}
          </div>
        );
      },
    },
  ];

  return (
    <ContentWrapper
      title="Contratos a iniciar"
      loading={loading}
    >
      <ContractsToStartTable
        ref={tableRef}
        columns={columns}
        onPageChange={(page) => setCurrentPage(page)}
        skipInitialLoad={skipInitialLoad}
        isInitialLoad={isInitialLoad}
      />
      <Sheet
        open={sheetOpen}
        onOpenChange={(val) => {
          // Se estiver fechando o form (val = false)
          if (!val) {
            // Fechar o form
            setSheetOpen(false);

            // Limpar o ID da proposta
            setEditingProposalId(null);

            // Definir a flag skipInitialLoad como false para permitir que o useEffect carregue dados normalmente na próxima vez
            setSkipInitialLoad(false);

            // Definir isInitialLoad como false para evitar que a tabela seja carregada na página 1
            setIsInitialLoad(false);

            // Atualizar a tabela com os filtros anteriores
            if (tableRef.current) {
              // Usar os valores do estado atual para atualizar a tabela
              // Isso garante que a tabela seja atualizada com a página e filtros corretos
              console.log("Atualizando tabela após fechar Sheet com valores do estado atual:", { currentPage, currentSearch, currentSituationFilter });

              // Usar um setTimeout para garantir que a tabela seja atualizada após o formulário ser fechado
              setTimeout(() => {
                tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
              }, 100);
            }
          }
        }}
      >
        <SheetContent className="w-[90%] sm:min-w-[450px] h-screen p-4">
          <SheetHeader>
            <SheetTitle />
            <SheetDescription />
          </SheetHeader>
          <ProposalForm
            scopes={serviceScopes}
            proposal={proposal}
            templates={templates}
            customers={customers}
            isContractValidated={true}
            isEditing={!!proposal}
            isLoading={formLoading}
            onChange={() => {
              // Limpar o ID da proposta
              setEditingProposalId(null);

              // Definir a flag skipInitialLoad como false para permitir que o useEffect carregue dados normalmente na próxima vez
              setSkipInitialLoad(false);

              // Definir isInitialLoad como false para evitar que a tabela seja carregada na página 1
              setIsInitialLoad(false);

              if (tableRef.current) {
                // Usar os valores do estado atual para atualizar a tabela
                // Isso garante que a tabela seja atualizada com a página e filtros corretos
                console.log("Atualizando tabela após salvar com valores do estado atual:", { currentPage, currentSearch, currentSituationFilter });

                // Usar um setTimeout para garantir que a tabela seja atualizada após o formulário ser fechado
                setTimeout(() => {
                  // Verificar se há uma URL da API salva no localStorage
                  if (typeof window !== 'undefined') {
                    const savedApiUrl = localStorage.getItem('lastContractApiUrl');
                    if (savedApiUrl) {
                      console.log("URL da API obtida do localStorage após salvar:", savedApiUrl);
                      // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
                      tableRef.current?.refreshWithApiUrl(savedApiUrl);
                      return;
                    }
                  }

                  // Se não houver URL da API salva, usar os valores do estado atual
                  tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
                }, 100);
              }
            }}
            onCancelClick={() => {
              setSheetOpen(false);

              // Limpar o ID da proposta
              setEditingProposalId(null);

              // Definir a flag skipInitialLoad como false para permitir que o useEffect carregue dados normalmente na próxima vez
              setSkipInitialLoad(false);

              // Definir isInitialLoad como false para evitar que a tabela seja carregada na página 1
              setIsInitialLoad(false);

              // Atualizar a tabela ao fechar o formulário com os filtros anteriores
              if (tableRef.current) {
                // Usar os valores do estado atual para atualizar a tabela
                // Isso garante que a tabela seja atualizada com a página e filtros corretos
                console.log("Atualizando tabela após cancelar com valores do estado atual:", { currentPage, currentSearch, currentSituationFilter });

                // Usar um setTimeout para garantir que a tabela seja atualizada após o formulário ser fechado
                setTimeout(() => {
                  // Verificar se há uma URL da API salva no localStorage
                  if (typeof window !== 'undefined') {
                    const savedApiUrl = localStorage.getItem('lastContractApiUrl');
                    if (savedApiUrl) {
                      console.log("URL da API obtida do localStorage após cancelar:", savedApiUrl);
                      // Usar o método refreshWithApiUrl para atualizar a tabela com a URL da API
                      tableRef.current?.refreshWithApiUrl(savedApiUrl);
                      return;
                    }
                  }

                  // Se não houver URL da API salva, usar os valores do estado atual
                  tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
                }, 100);
              }
            }}
            onStatusChange={async (proposalId, newStatus) => {
              try {
                // Implementar a lógica para atualizar o status da proposta
                const updatedProposals = proposals.map(p => {
                  if (p.id === proposalId) {
                    return { ...p, situation: newStatus as ProposalSituation };
                  }
                  return p;
                });
                setProposals(updatedProposals);

                // Fechar o formulário
                setSheetOpen(false);

                // Limpar o ID da proposta
                setEditingProposalId(null);

                // Definir a flag skipInitialLoad como false para permitir que o useEffect carregue dados normalmente na próxima vez
                setSkipInitialLoad(false);

                // Definir isInitialLoad como false para evitar que a tabela seja carregada na página 1
                setIsInitialLoad(false);

                // Atualizar a tabela mantendo a página e filtros atuais
                if (tableRef.current) {
                  // Usar diretamente os valores do estado atual
                  // Isso garante que a tabela seja atualizada com a página e filtros corretos
                  console.log("Atualizando tabela após atualizar status com valores do estado atual:", { currentPage, currentSearch, currentSituationFilter });

                  // Usar um setTimeout para garantir que a tabela seja atualizada após o formulário ser fechado
                  setTimeout(() => {
                    tableRef.current?.refresh(currentPage, currentSearch, currentSituationFilter);
                  }, 100);
                }
              } catch (error) {
                console.error("Erro ao atualizar status:", error);
                toast({
                  title: "Erro",
                  description: "Erro ao atualizar o status do contrato",
                  variant: "destructive"
                });
              }
            }}
          />
        </SheetContent>
      </Sheet>
    </ContentWrapper>
  );
}
