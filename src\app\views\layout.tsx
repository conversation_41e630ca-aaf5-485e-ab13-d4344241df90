import AppBreadcrumbs from "@/src/components/app-breadcrumbs";
import { AppSidebar } from "@/src/components/app-sidebar";
import { auth } from "@/src/providers/auth";
import { prisma } from "@/src/lib/prisma";
import { SessionProvider } from "next-auth/react";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import Loading from "@/src/components/app-loading";

export default async function layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();

  // If user is not logged in, redirect to auth page
  if (!session?.user) {
    return redirect("/auth");
  }

  // Check if user has an active membership
  const membership = await prisma.membership.findFirst({
    where: {
      userId: session.user.id,
      enabled: true
    }
  });

  // If no active membership, redirect to welcome page
  if (!membership) {
    return redirect("/welcome");
  }

  return (
    <SessionProvider>
      <div className="flex h-[100svh] viewport-height overflow-hidden relative">
        <AppSidebar
          user={session.user}
          membership={session.membership}
        />
        <main className="flex-1 h-[100svh] viewport-height bg-gray-100 p-3 sm:p-4 overflow-scroll custom-scrollbar w-full pb-40 sm:pb-4">
          <AppBreadcrumbs />
          <div className="mt-3 sm:mt-5">
            <Suspense fallback={<Loading />}>
              {children}
            </Suspense>
          </div>
        </main>
      </div>
    </SessionProvider>
  );
}