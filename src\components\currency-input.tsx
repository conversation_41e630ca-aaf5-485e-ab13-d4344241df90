import React from 'react';
import { NumericFormat } from 'react-number-format';
import { Label } from './ui/label';

interface CurrencyInputProps {
  value: number | undefined;
  onChange: (value: number) => void;
  label?: string;
  placeholder?: string;
  name?: string;
  required?: boolean;
  hideErrorMessage?: boolean;
  disabled?: boolean;
  maxValue?: number;
  errorMessage?: string;
  isFormSubmitted?: boolean;
  isTouched?: boolean;
  onBlur?: () => void;
}

export function CurrencyInput({
  value,
  onChange,
  label,
  name,
  disabled = false,
  placeholder = 'R$ 0,00',
  hideErrorMessage = false,
  maxValue,
  errorMessage = 'Valor excede o limite máximo permitido',
  isFormSubmitted = false,
  isTouched = false,
  onBlur,
}: CurrencyInputProps) {
  // Estado para controlar se o campo foi tocado internamente
  const [fieldTouched, setFieldTouched] = React.useState(false);

  // Determinar se deve mostrar o erro
  const [internalError, setInternalError] = React.useState<string | null>(null);

  // Adicionar estado para controlar se é a primeira renderização
  const [isInitialRender, setIsInitialRender] = React.useState(true);

  // Verificar se o formulário foi submetido através do localStorage
  const [wasFormSubmitted, setWasFormSubmitted] = React.useState(false);

  // Usar useEffect para atualizar o estado após a primeira renderização
  React.useEffect(() => {
    // Após o componente ser montado, não é mais a renderização inicial
    setIsInitialRender(false);
  }, []);

  // Verificar o localStorage apenas uma vez após a renderização inicial
  React.useEffect(() => {
    if (!isInitialRender) {
      setWasFormSubmitted(typeof window !== 'undefined' && localStorage.getItem('proposal_form_submitted') === 'true');
    }
  }, [isInitialRender]);

  React.useEffect(() => {
    // Não validar na primeira renderização ou se o campo não foi tocado e o formulário não foi submetido
    if (isInitialRender || (!fieldTouched && !isTouched && !isFormSubmitted && !wasFormSubmitted)) {
      return;
    }

    // Verificar se o valor é um número válido
    const numValue = typeof value === 'number' ? value : 0;

    // Verifica se o valor excede o máximo permitido
    if (maxValue !== undefined && numValue > maxValue) {
      setInternalError(errorMessage);
    } else if (numValue <= 0) {
      // Determinar a mensagem de erro com base no nome do campo
      if (name === 'downPayment') {
        if (numValue === 0) {
          setInternalError('Entrada é obrigatório');
        } else {
          setInternalError('Entrada deve ser maior que 0');
        }
      } else {
        if (numValue === 0) {
          setInternalError('Orçamento é obrigatório');
        } else {
          setInternalError('Orçamento deve ser maior que 0');
        }
      }
    } else {
      // Se o valor for maior que zero, não há erro
      setInternalError(null);
    }
  }, [value, maxValue, errorMessage, name, isInitialRender, fieldTouched, isTouched, isFormSubmitted, wasFormSubmitted]);

  // Determinar se deve mostrar o erro com base no estado do formulário
  const showError = internalError && (isTouched || fieldTouched || isFormSubmitted);

  // Verificar se o valor é zero ou vazio (para mostrar erro de campo obrigatório)
  const isValueZeroOrEmpty = value === undefined || value === 0;

  // Verificar se o valor é maior que zero (para não mostrar erro)
  const isValueGreaterThanZero = value !== undefined && value > 0;

  // Verificar se há uma mensagem de erro externa
  const hasExternalError = !!errorMessage && errorMessage !== 'Valor excede o limite máximo permitido';

  // Mostrar erro se o formulário foi submetido via localStorage ou se o valor é zero/vazio e o formulário foi submetido
  // ou se há uma mensagem de erro externa, mas nunca na renderização inicial
  // Adicionalmente, não mostrar erro se o campo não foi tocado e o formulário não foi submetido
  // E nunca mostrar erro se o valor for maior que zero
  const shouldShowError = !isInitialRender && !isValueGreaterThanZero && (
    (showError && (fieldTouched || isTouched || isFormSubmitted || wasFormSubmitted)) ||
    (internalError && wasFormSubmitted) ||
    (isValueZeroOrEmpty && (isFormSubmitted || wasFormSubmitted)) ||
    (hasExternalError && (fieldTouched || isTouched || isFormSubmitted || wasFormSubmitted))
  );

  const handleValueChange = (values: any) => {
    // Converte para número quando o onChange é chamado
    const newValue = values.floatValue !== undefined ? values.floatValue : 0;

    // Limpar o erro se o valor for maior que zero
    if (newValue > 0) {
      setInternalError(null);
    }

    onChange(newValue);
  };

  const handleBlur = () => {
    // Marcar o campo como tocado internamente
    setFieldTouched(true);

    // Chamar o onBlur passado como prop, se existir
    if (onBlur) {
      onBlur();
    }
  };

  return (
    <div className="grid gap-2">
      {label && (
        <div className="flex mb-2">
          <Label htmlFor={name} className="font-bold text-gray-700">
            {label}
          </Label>
        </div>
      )}
      <div className="relative">
        <NumericFormat
          id={name}
          name={name}
          value={value}
          thousandSeparator="."
          decimalSeparator=","
          decimalScale={2}
          fixedDecimalScale
          prefix="R$ "
          placeholder={placeholder}
          onValueChange={handleValueChange}
          onBlur={handleBlur}
          disabled={disabled}
          className={`flex h-10 w-full rounded-md border ${shouldShowError || (internalError && !isValueGreaterThanZero) ? 'border-red-500 focus-visible:ring-red-500' : 'border-input'} bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50`}
        />
        {shouldShowError && !hideErrorMessage && (
          <p className="text-destructive text-[0.8rem] font-bold mt-1 animate-fadeIn">
            {hasExternalError ? errorMessage : internalError}
          </p>
        )}
      </div>
    </div>
  );
}
