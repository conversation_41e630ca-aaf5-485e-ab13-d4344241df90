"use client";

import { InspectionParameter } from "@/src/types/core/inspection-paramenters";
import { formatDate, toKebabCase } from "@/src/lib/utils";
import { Proposal } from "@/src/types/core/proposal";
import { Customer } from "@/src/types/core/customer";
import { prisma } from "@/src/lib/prisma";
import logoImg from "@public/logos/img8.jpg";

interface DirectInspectionPdfGeneratorProps {
  inspection: InspectionParameter;
  proposal: Proposal;
  customer: Customer;
}

// Função para obter dados de pluviosidade para uma inspeção
async function getPluviosityForInspection(city: string, inspectionDate: Date) {
  const dateKey = inspectionDate.toISOString().split("T")[0];
  const pluviosity = await prisma.pluviosity.findFirst({
    where: {
      dateKey,
      city: toKebabCase(city),
    },
    select: {
      value: true,
    },
  });

  return pluviosity?.value?.toString() ?? "0";
}

// Função para determinar a condição climática com base no valor de pluviosidade
function getWeatherCondition(pluviosityValue: string): string {
  const value = parseFloat(pluviosityValue);

  if (value === 0) return "Dia claro, sem precipitação";
  if (value <= 0.2) return "Garoa leve";
  if (value <= 2.5) return "Chuva fraca";
  if (value <= 7.6) return "Chuva moderada";
  if (value <= 50) return "Chuva forte";
  return "Chuva muito forte";
}

export async function generateDirectInspectionPdf({
  inspection,
  proposal,
  customer,
}: DirectInspectionPdfGeneratorProps) {
  try {
    // Função auxiliar para carregar uma imagem de forma síncrona
    const loadImageSync = (url: string): Promise<HTMLImageElement> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'Anonymous';
        img.onload = () => resolve(img);
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    };
    // Importar as bibliotecas dinamicamente
    const jspdfModule = await import('jspdf');
    const jsPDF = jspdfModule.jsPDF;

    // Declarar a variável autoTable no escopo da função
    let autoTable: any;

    // Importar jspdf-autotable
    try {
      const autoTableModule = await import('jspdf-autotable');
      autoTable = autoTableModule.default;
    } catch (error) {
      console.error("Erro ao importar jspdf-autotable:", error);
      // Criar uma função alternativa para autoTable caso a biblioteca não esteja disponível
      autoTable = function (pdf: any, options: any) {
        const { startY, head, body, styles, columnStyles, margin } = options;
        let y = startY || 20;
        const lineHeight = 10;

        // Desenhar cabeçalho
        if (head && head.length > 0) {
          const headerRow = head[0];
          let x = margin?.left || 15;
          const cellWidth = (pdf.internal.pageSize.getWidth() - (margin?.left || 15) * 2) / headerRow.length;

          pdf.setFillColor(220, 220, 220);
          pdf.setTextColor(0, 0, 0);
          pdf.setFontSize(styles?.fontSize || 10);
          pdf.setFont("helvetica", "bold");

          headerRow.forEach((cell: any, index: number) => {
            pdf.rect(x, y, columnStyles?.[index]?.cellWidth || cellWidth, lineHeight, 'FD');
            pdf.text(cell, x + 2, y + lineHeight / 2 + 2);
            x += columnStyles?.[index]?.cellWidth || cellWidth;
          });

          y += lineHeight;
        }

        // Desenhar corpo
        if (body && body.length > 0) {
          pdf.setFillColor(255, 255, 255);
          pdf.setTextColor(0, 0, 0);
          pdf.setFontSize(styles?.fontSize || 10);
          pdf.setFont("helvetica", "normal");

          body.forEach((row: any[]) => {
            let x = margin?.left || 15;
            const cellWidth = (pdf.internal.pageSize.getWidth() - (margin?.left || 15) * 2) / row.length;

            row.forEach((cell: any, index: number) => {
              pdf.rect(x, y, columnStyles?.[index]?.cellWidth || cellWidth, lineHeight, 'D');
              pdf.text(String(cell), x + 2, y + lineHeight / 2 + 2);
              x += columnStyles?.[index]?.cellWidth || cellWidth;
            });

            y += lineHeight;
          });
        }

        // Adicionar propriedade para compatibilidade
        pdf.lastAutoTable = { finalY: y };

        return pdf;
      };
    }

    // Criar um novo documento PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Configurações de margens
    const margin = 15;
    const contentWidth = pageWidth - 2 * margin;

    // Adicionar propriedade lastAutoTable ao objeto pdf para compatibilidade
    (pdf as any).lastAutoTable = { finalY: margin };

    // Título do relatório
    const inspectionDate = inspection.inspectionDate
      ? formatDate(inspection.inspectionDate, "DATE")
      : formatDate(new Date(), "DATE");

    // Função para adicionar cabeçalho consistente em todas as páginas
    const addHeader = () => {
      // Adicionar cabeçalho
      pdf.setFillColor(255, 255, 255);
      pdf.rect(margin, margin, contentWidth, 20, 'F');

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Relatório ${inspectionDate} até ${inspectionDate} nº ${inspection.numberInspection || ''}`, pageWidth / 2, margin + 8, { align: "center" });

      // Não adicionar texto de continuação, apenas manter o cabeçalho consistente
    };

    // Adicionar cabeçalho na primeira página
    addHeader();

    // Adicionar logo centralizada usando a imagem importada
    // Calcular a posição central para a logo
    const logoWidth = 80;
    const logoHeight = 30;
    const logoX = (pageWidth - logoWidth) / 2;

    try {
      // Converter a imagem SVG importada para uma URL de dados
      const logoUrl = logoImg.src;

      // Criar uma imagem para carregar o logo
      const img = new Image();
      img.src = logoUrl;

      // Adicionar a imagem ao PDF
      pdf.addImage(img, 'PNG', logoX, margin + 10, logoWidth, logoHeight);
    } catch (error) {
      console.error("Erro ao adicionar logo:", error);

      // Fallback: Adicionar um retângulo colorido como placeholder
      pdf.setFillColor(240, 240, 240);
      pdf.rect(logoX, margin + 10, logoWidth, logoHeight, 'F');

      // Adicionar texto centralizado
      pdf.setFontSize(12);
      pdf.setFont("helvetica", "bold");
      pdf.text("LOGO", logoX + logoWidth / 2, margin + 25, { align: "center" });
    }

    // Informações do relatório
    let yPos = margin + 45;

    // Tabela de informações do relatório (largura completa)
    autoTable(pdf, {
      startY: yPos,
      head: [['', '']],
      body: [
        ['Relatório nº', `${inspection.numberInspection || ''}`],
        ['Data do relatório', `${inspectionDate} - ${inspectionDate}`],
        ['Dia da semana', `${new Date(inspection.inspectionDate).toLocaleDateString('pt-BR', { weekday: 'long' })} até ${new Date(inspection.inspectionDate).toLocaleDateString('pt-BR', { weekday: 'long' })}`]
      ],
      theme: 'grid',
      headStyles: { fillColor: [220, 220, 220], textColor: [0, 0, 0], fontStyle: 'bold' },
      styles: { fontSize: 9, cellPadding: 2 },
      columnStyles: { 0: { fontStyle: 'bold', cellWidth: 40 }, 1: { cellWidth: contentWidth - 40 } },
      tableWidth: contentWidth,
      margin: { left: margin }
    });

    // Título do relatório periódico - usar uma tabela para manter a consistência
    yPos = (pdf as any).lastAutoTable.finalY + 10;
    autoTable(pdf, {
      startY: yPos,
      head: [['Relatório Periódico de Obra (RPO)']],
      body: [],
      theme: 'grid',
      headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold', fontSize: 10 },
      styles: { fontSize: 9, cellPadding: 2, minCellHeight: 10, valign: 'middle' },
      columnStyles: { 0: { cellWidth: contentWidth } },
      tableWidth: contentWidth,
      margin: { left: margin }
    });

    // Definir configurações comuns para todas as tabelas de informações
    const commonTableStyles = {
      fontSize: 9,
      cellPadding: 2,
      minCellHeight: 10, // Altura mínima fixa para todas as células
      valign: 'middle'   // Alinhamento vertical centralizado
    };

    const commonHeadStyles = {
      fillColor: [220, 220, 220],
      textColor: [0, 0, 0],
      fontStyle: 'bold',
      minCellHeight: 10,
      valign: 'middle'
    };

    // Calcular prazos com base nas datas da proposta
    let prazoContratual = '';
    let prazoDecorrido = '';
    let prazoVencer = '';

    if (proposal?.startDate && proposal?.endDate) {
      const startDate = new Date(proposal.startDate);
      const endDate = new Date(proposal.endDate);
      const today = new Date();

      // Calcular prazo contratual (total de dias do contrato)
      const diffTimeContratual = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDaysContratual = Math.ceil(diffTimeContratual / (1000 * 60 * 60 * 24));
      prazoContratual = `${diffDaysContratual} dias`;

      // Calcular prazo decorrido (dias desde o início até hoje)
      const diffTimeDecorrido = Math.abs(today.getTime() - startDate.getTime());
      const diffDaysDecorrido = Math.ceil(diffTimeDecorrido / (1000 * 60 * 60 * 24));
      prazoDecorrido = `${diffDaysDecorrido} dias`;

      // Calcular prazo a vencer (dias restantes até o fim)
      const diffTimeVencer = endDate.getTime() - today.getTime();
      const diffDaysVencer = Math.ceil(diffTimeVencer / (1000 * 60 * 60 * 24));
      prazoVencer = `${diffDaysVencer} dias`;
    }

    // Tabela de informações da obra
    yPos += 15;
    autoTable(pdf, {
      startY: yPos,
      head: [['Obra', 'Prazo contratual']],
      body: [
        [
          proposal?.name || '',
          prazoContratual
        ]
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: { 0: { cellWidth: contentWidth - 80 }, 1: { cellWidth: 80 } },
      tableWidth: contentWidth
    });

    // Tabela de local
    yPos = (pdf as any).lastAutoTable.finalY;
    autoTable(pdf, {
      startY: yPos,
      head: [['Local', 'Prazo decorrido']],
      body: [
        [
          proposal?.address && proposal?.city ? `${proposal.address}, ${proposal.city}${proposal.state ? `, ${proposal.state}` : ''}` : '',
          prazoDecorrido
        ]
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: { 0: { cellWidth: contentWidth - 80 }, 1: { cellWidth: 80 } },
      tableWidth: contentWidth
    });

    // Tabela de contratante
    yPos = (pdf as any).lastAutoTable.finalY;
    autoTable(pdf, {
      startY: yPos,
      head: [['Contratante', 'Prazo a vencer']],
      body: [
        [
          customer?.name || '',
          prazoVencer
        ]
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: { 0: { cellWidth: contentWidth - 80 }, 1: { cellWidth: 80 } },
      tableWidth: contentWidth
    });

    // Tabela de condição climática
    yPos = (pdf as any).lastAutoTable.finalY + 10;

    // Obter dados de pluviosidade
    let pluviosityValue = "0";
    try {
      if (proposal?.city && inspection.inspectionDate) {
        // Garantir que inspectionDate seja uma instância de Date
        const inspectionDateObj = inspection.inspectionDate instanceof Date ?
          inspection.inspectionDate : new Date(inspection.inspectionDate);
        pluviosityValue = await getPluviosityForInspection(proposal.city, inspectionDateObj);
      }
    } catch (error) {
      console.error("Erro ao obter dados de pluviosidade:", error);
    }

    // Determinar condição climática
    const weatherCondition = getWeatherCondition(pluviosityValue);

    // Determinar se é praticável
    const isPracticable = parseFloat(pluviosityValue) <= 2.5 ? "Praticável" : "Não Praticável";

    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Condição Climática", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['Precipitação (mm)', 'Condição Climática', 'Situação']],
      body: [
        [pluviosityValue, weatherCondition, isPracticable]
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: { 0: { cellWidth: 40 }, 1: { cellWidth: contentWidth - 120 }, 2: { cellWidth: 80 } },
      tableWidth: contentWidth
    });

    // Tabela de mão de obra
    yPos = (pdf as any).lastAutoTable.finalY + 10;

    // Preparar dados de mão de obra
    const laborData = inspection.laborEquipament?.filter(item =>
      item.labor.type === 'LABOR'
    ) || [];

    // Preparar array para dados de mão de obra
    let totalLabor = 0;

    // Verificar se há dados de mão de obra
    if (laborData.length > 0) {
      // Obter categorias únicas de mão de obra dos dados
      const uniqueCategories = [...new Set(laborData.map(item => item.labor.name))];
      const laborCounts = {};

      // Inicializar contagens
      uniqueCategories.forEach(category => {
        laborCounts[category] = 0;
      });

      // Contar mão de obra por categoria
      laborData.forEach(item => {
        const category = item.labor.name;
        laborCounts[category] = (laborCounts[category] || 0) + (item.amount || 0);
        totalLabor += (item.amount || 0);
      });

      // Criar linha para a tabela
      const laborRow = uniqueCategories.map(category => laborCounts[category] || 0);

      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Mão de obra (${totalLabor})`, margin, yPos);

      yPos += 5;
      // Calcular largura igual para cada coluna
      const columnCount = uniqueCategories.length;
      const equalWidth = contentWidth / columnCount;

      // Criar objeto de estilos de coluna com larguras iguais
      const columnStylesObj = {};
      uniqueCategories.forEach((_, index) => {
        columnStylesObj[index] = { cellWidth: equalWidth };
      });

      autoTable(pdf, {
        startY: yPos,
        head: [uniqueCategories],
        body: [laborRow],
        theme: 'grid',
        headStyles: { ...commonHeadStyles, halign: 'center' },
        styles: { ...commonTableStyles, halign: 'center' },
        columnStyles: columnStylesObj,
        tableWidth: contentWidth
      });

      // Adicionar "Mão de Obra Direta" no final da tabela
      pdf.setFontSize(9);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Mão de Obra Direta (${totalLabor})`, pageWidth - margin - 50, (pdf as any).lastAutoTable.finalY + 5);
    } else {
      // Exibir mensagem quando não há dados de mão de obra
      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text("Mão de obra", margin, yPos);

      yPos += 5;
      autoTable(pdf, {
        startY: yPos,
        head: [['Informação']],
        body: [['Nenhuma mão de obra foi adicionada a esta inspeção.']],
        theme: 'grid',
        headStyles: { ...commonHeadStyles, halign: 'center' },
        styles: { ...commonTableStyles, halign: 'center' },
        columnStyles: { 0: { cellWidth: contentWidth } },
        tableWidth: contentWidth
      });
    }

    // Verificar se há espaço suficiente para a tabela de equipamentos
    // Se não houver, adicionar uma nova página
    if ((pdf as any).lastAutoTable.finalY + 100 > pageHeight) {
      pdf.addPage();
      addHeader();
      yPos = margin + 30; // Posição inicial após o cabeçalho na nova página
    } else {
      yPos = (pdf as any).lastAutoTable.finalY + 15;
    }

    // Preparar dados de equipamentos - remover duplicatas
    const equipmentData = inspection.laborEquipament?.filter(item =>
      item.labor.type === 'EQUIPAMENT'
    ) || [];

    // Remover duplicatas baseado no laborId
    const uniqueEquipmentMap = new Map();
    equipmentData.forEach(item => {
      if (!uniqueEquipmentMap.has(item.laborId)) {
        uniqueEquipmentMap.set(item.laborId, item);
      }
    });

    // Converter o Map de volta para array
    const uniqueEquipmentData = Array.from(uniqueEquipmentMap.values());

    // Exibir apenas uma tabela de equipamentos
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text(`Equipamentos${uniqueEquipmentData.length > 0 ? ` (${uniqueEquipmentData.length})` : ''}`, margin, yPos);

    yPos += 5;

    // Verificar se há dados de equipamentos
    if (uniqueEquipmentData.length > 0) {
      // Obter categorias únicas de equipamentos dos dados
      const uniqueCategories = [...new Set(uniqueEquipmentData.map(item => item.labor.name))];
      const equipmentCounts = {};

      // Inicializar contagens
      uniqueCategories.forEach(category => {
        equipmentCounts[category] = 0;
      });

      // Contar equipamentos por categoria
      uniqueEquipmentData.forEach(item => {
        const category = item.labor.name;
        equipmentCounts[category] = (equipmentCounts[category] || 0) + (item.amount || 0);
      });

      // Criar linha para a tabela
      const equipmentRow = uniqueCategories.map(category => equipmentCounts[category] || 0);

      // Calcular largura igual para cada coluna
      const columnCount = uniqueCategories.length;
      const equalWidth = contentWidth / columnCount;

      // Criar objeto de estilos de coluna com larguras iguais
      const columnStylesObj = {};
      uniqueCategories.forEach((_, index) => {
        columnStylesObj[index] = { cellWidth: equalWidth };
      });

      // Adicionar a tabela com opção de não quebrar entre páginas
      autoTable(pdf, {
        startY: yPos,
        head: [uniqueCategories],
        body: [equipmentRow],
        theme: 'grid',
        headStyles: { ...commonHeadStyles, halign: 'center' },
        styles: { ...commonTableStyles, halign: 'center' },
        columnStyles: columnStylesObj,
        tableWidth: contentWidth,
        // Configurações para evitar quebra de página no meio da tabela
        willDrawPage: () => {
          // Não adicionar texto de continuação
        },
        didDrawPage: (data) => {
          // Resetar a posição Y após a quebra de página
          yPos = data.cursor.y;
        },
        // Definir margens para evitar que o conteúdo fique muito próximo das bordas
        margin: { top: margin + 25, left: margin, right: margin, bottom: margin }
      });
    } else {
      // Exibir mensagem quando não há dados de equipamentos
      autoTable(pdf, {
        startY: yPos,
        head: [['Informação']],
        body: [['Nenhum equipamento foi adicionado a esta inspeção.']],
        theme: 'grid',
        headStyles: { ...commonHeadStyles, halign: 'center' },
        styles: { ...commonTableStyles, halign: 'center' },
        columnStyles: { 0: { cellWidth: contentWidth } },
        tableWidth: contentWidth
      });
    }

    // Tabela de atividades (campo technicalData)
    yPos = (pdf as any).lastAutoTable.finalY + 15;
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Atividades Técnicas (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['Dados Técnicos', 'Status']],
      body: [
        [inspection.technicalData || 'Sem dados técnicos', 'Em Andamento']
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: commonTableStyles,
      columnStyles: { 0: { cellWidth: contentWidth - 80 }, 1: { cellWidth: 80 } },
      tableWidth: contentWidth
    });

    // Tabela de comentários (campo observation)
    yPos = (pdf as any).lastAutoTable.finalY + 15;
    pdf.setFontSize(11);
    pdf.setFont("helvetica", "bold");
    pdf.text("Observações (1)", margin, yPos);

    yPos += 5;
    autoTable(pdf, {
      startY: yPos,
      head: [['Comentários e Observações', '']],
      body: [
        [`Cliente: ${customer?.name || 'Não informado'}\nData: ${formatDate(inspection.inspectionDate, "DATETIME")}\n\nObservação: ${inspection.observation || 'Sem observações'}`, '']
      ],
      theme: 'grid',
      headStyles: commonHeadStyles,
      styles: { ...commonTableStyles, minCellHeight: 20 },
      columnStyles: { 0: { cellWidth: contentWidth - 10 }, 1: { cellWidth: 10 } },
      tableWidth: contentWidth
    });

    // Adicionar fotos se disponíveis
    if (inspection.photos && inspection.photos.length > 0) {
      // Adicionar nova página para fotos
      pdf.addPage();

      // Adicionar cabeçalho consistente
      addHeader();

      yPos = margin + 20;
      pdf.setFontSize(11);
      pdf.setFont("helvetica", "bold");
      pdf.text(`Fotos (${inspection.photos.length})`, margin, yPos);

      // Não adicionar texto de informação adicional

      yPos += 15;

      // Adicionar fotos em grade 2x2 com tamanhos consistentes
      const photoWidth = (contentWidth - 10) / 2; // Largura igual para cada coluna com pequeno espaçamento
      const photoHeight = photoWidth * 0.75; // Proporção 4:3

      // Processar as fotos em lotes de 4
      for (let i = 0; i < inspection.photos.length; i += 4) {
        // Verificar se precisa adicionar nova página
        if (yPos + photoHeight * 2 + 20 > pageHeight) {
          pdf.addPage();
          // Adicionar cabeçalho consistente
          addHeader();
          yPos = margin + 20;

          // Não adicionar texto de continuação
          yPos += 5;
        }

        // Processar até 4 fotos por vez (2 linhas de 2 fotos)
        const photosInBatch = inspection.photos.slice(i, i + 4);

        // Processar cada foto no lote
        for (let j = 0; j < photosInBatch.length; j++) {
          const photo = photosInBatch[j];
          const col = j % 2;
          const row = Math.floor(j / 2);

          const xPos = margin + col * (photoWidth + 10);
          const yPhotoPos = yPos + row * (photoHeight + 10);

          // Função para adicionar placeholder em caso de erro
          const addPlaceholder = () => {
            pdf.setFillColor(200, 200, 200);
            pdf.rect(xPos, yPhotoPos, photoWidth, photoHeight, 'F');

            // Adicionar descrição da foto
            if (photo.description) {
              pdf.setFontSize(8);
              pdf.setFont("helvetica", "normal");
              pdf.text(photo.description, xPos, yPhotoPos + photoHeight + 5, { maxWidth: photoWidth });
            }
          };

          try {
            // Tentar carregar a imagem real
            if (photo.file && photo.fileId) {
              try {
                // Construir a URL da imagem
                const imageUrl = window.location.origin + `/api/files/${encodeURIComponent(photo.fileId)}`;

                // Carregar a imagem de forma síncrona
                const img = await loadImageSync(imageUrl);

                // Adicionar a imagem ao PDF
                pdf.addImage(img, 'JPEG', xPos, yPhotoPos, photoWidth, photoHeight);

                // Adicionar descrição da foto
                if (photo.description) {
                  pdf.setFontSize(8);
                  pdf.setFont("helvetica", "normal");
                  pdf.text(photo.description, xPos, yPhotoPos + photoHeight + 5, { maxWidth: photoWidth });
                }

                console.log(`Imagem adicionada com sucesso: ${imageUrl}`);
              } catch (err) {
                console.error("Erro ao carregar ou adicionar imagem ao PDF:", err);
                addPlaceholder();
              }
            } else {
              // Se não houver arquivo, usar placeholder
              console.log("Arquivo de imagem não encontrado, usando placeholder");
              addPlaceholder();
            }
          } catch (error) {
            console.error("Erro ao processar imagem:", error);
            addPlaceholder();
          }
        }

        yPos += photoHeight * 2 + 20;
      }
    }

    // Adicionar número de página
    const totalPages = pdf.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "normal");
      pdf.text(`${i} / ${totalPages}`, pageWidth - margin, pageHeight - margin - 2);
    }

    // Gerar o PDF como um blob URL em vez de salvá-lo diretamente
    const pdfBlob = pdf.output('blob');
    const blobUrl = URL.createObjectURL(pdfBlob);
    const fileName = `relatorio_inspecao_${inspection.numberInspection || Date.now()}.pdf`;

    return {
      success: true,
      blobUrl,
      fileName,
      blob: pdfBlob
    };
  } catch (error) {
    console.error("Erro ao gerar PDF:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
