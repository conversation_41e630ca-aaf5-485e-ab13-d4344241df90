"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";

import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
	SidebarGroup,
	SidebarMenu,
	SidebarMenuAction,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarMenuSub,
	SidebarMenuSubButton,
	SidebarMenuSubItem,
	useSidebar,
} from "@/components/ui/sidebar";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";
import Link from "next/link";
import { cn } from "../lib/utils";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "./ui/dropdown-menu";

interface Item {
	title: string;
	url: string;
	icon: LucideIcon;
	items?: Item[];
	disableLink?: boolean;
}

interface NavMainProps {
	items: Item[];
	pathname: string;
}

export function NavMain({ items, pathname }: NavMainProps) {
	const { state } = useSidebar();

	const activeClass = "!font-semibold !text-green-500 !bg-green-100";
	const hoverClass = "hover:!text-green-500 hover:!bg-green-100";

	return (
		<SidebarGroup>
			<SidebarMenu className="flex flex-col gap-3">
				{items.map((item) => (
					<Collapsible key={item.title} asChild>
						<SidebarMenuItem>
							<SidebarMenuButton
								asChild
								tooltip={item.title}
								className={hoverClass}
							>
								{item.items?.length ? (
									<CollapsibleTrigger>
										<div className={cn(hoverClass, "flex gap-2 items-center")}>
											{state == "expanded" ? (
												<item.icon className="w-4 h-4" />
											) : (
												<DropdownMenu>
													{state == "collapsed" && (
														<DropdownMenuTrigger asChild>
															<item.icon className="w-4 h-4 cursor-pointer" />
														</DropdownMenuTrigger>
													)}

													<DropdownMenuContent className="ml-10 p-1 flex flex-col gap-2">
														{item.items.map((subItem, index) => (
															<DropdownMenuItem key={index}>
																{subItem.disableLink ? (
																	<div className="flex gap-2 items-center p-1 opacity-50 cursor-not-allowed">
																		<subItem.icon className="w-4 h-4" />
																		<span className="!text-[14px]">
																			{subItem.title}
																		</span>
																	</div>
																) : (
																	<Link
																		href={subItem.url}
																		className={cn(
																			"flex gap-2 items-center p-1",
																			hoverClass,
																			pathname == subItem.url && activeClass
																		)}
																		prefetch={true}
																	>
																		<subItem.icon className="w-4 h-4" />
																		<span className="!text-[14px]">
																			{subItem.title}
																		</span>
																	</Link>
																)}
															</DropdownMenuItem>
														))}
													</DropdownMenuContent>
												</DropdownMenu>
											)}

											<span>{state == "expanded" && item.title}</span>
										</div>
									</CollapsibleTrigger>
								) : (
									<Link
										href={item.url}
										className={cn(hoverClass, pathname == item.url && activeClass)}
										prefetch={true}
									>
										<item.icon />
										<span>{item.title}</span>
									</Link>
								)}
							</SidebarMenuButton>
							{item.items?.length && (
								<>
									<CollapsibleTrigger asChild>
										<SidebarMenuAction className="data-[state=open]:rotate-90">
											<ChevronRight />
											<span className="sr-only">Toggle</span>
										</SidebarMenuAction>
									</CollapsibleTrigger>
									<CollapsibleContent>
										<SidebarMenuSub>
											{item.items?.map((subItem) => (
												<SidebarMenuSubItem key={subItem.title}>
													<SidebarMenuSubButton asChild>
														{subItem.disableLink ? (
															<div className="flex items-center gap-2 px-2 py-1 opacity-50 cursor-not-allowed">
																<div>
																	<subItem.icon className="w-4 h-4" />
																</div>
																<span>{subItem.title}</span>
															</div>
														) : (
															<Link
																href={subItem.url}
																className={cn(
																	hoverClass,
																	pathname == subItem.url && activeClass
																)}
																prefetch={true}
															>
																<div className="hover:!text-green-500">
																	<subItem.icon className="w-4 h-4" />
																</div>

																<span
																	className={cn(
																		hoverClass,
																		pathname == subItem.url && activeClass
																	)}
																>
																	{subItem.title}
																</span>
															</Link>
														)}
													</SidebarMenuSubButton>
												</SidebarMenuSubItem>
											))}
										</SidebarMenuSub>
									</CollapsibleContent>
								</>
							)}
						</SidebarMenuItem>
					</Collapsible>
				))}
			</SidebarMenu>
		</SidebarGroup>
	);
}
