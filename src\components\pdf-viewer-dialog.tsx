"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  // DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Download, Loader2, Send, Link, X } from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "@/src/hooks/use-toast";
import { generatePublicReportUrl } from "@/src/lib/report-token";

interface PdfViewerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pdfUrl: string;
  fileName: string;
  pdfBlob?: Blob;
  showEmailButton?: boolean;
  customerEmail?: string;
  customerName?: string;
  proposalName?: string;
  inspectionId?: string;
  reportType?: 'inspection' | 'project';
}

export default function PdfViewerDialog({
  open,
  onOpenChange,
  pdfUrl,
  fileName,
  pdfBlob,
  showEmailButton = false,
  customerEmail,
  customerName,
  proposalName,
  inspectionId,
  reportType = 'inspection',
}: PdfViewerDialogProps) {
  const [loading, setLoading] = useState(true);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [publicUrl, setPublicUrl] = useState<string | null>(null);

  // Efeito para resetar o estado de carregamento quando a URL do PDF mudar
  useEffect(() => {
    if (pdfUrl) {
      setLoading(true);
    }
  }, [pdfUrl]);

  // Efeito para gerar a URL pública quando o componente for montado
  useEffect(() => {
    if (inspectionId && showEmailButton) {
      try {
        console.log("Gerando URL pública para:", { inspectionId, reportType });
        const url = generatePublicReportUrl(inspectionId, reportType);
        console.log("URL pública gerada:", url);
        setPublicUrl(url);
      } catch (error) {
        console.error("Erro ao gerar URL pública:", error);
      }
    }
  }, [inspectionId, reportType, showEmailButton]);

  const handleDownload = () => {
    // Create a temporary link element
    const link = document.createElement("a");
    link.href = pdfUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Função para criar um arquivo temporário a partir do blob do PDF
  const createFileFromBlob = (blob: Blob, fileName: string): File => {
    return new File([blob], fileName, { type: 'application/pdf' });
  };

  // Função para enviar o PDF por e-mail
  const handleSendEmail = async () => {
    if (!pdfBlob || !customerEmail) {
      toast({
        title: "Erro",
        description: "Não foi possível enviar o relatório. Dados incompletos.",
        variant: "destructive"
      });
      return;
    }

    try {
      setSendingEmail(true);

      // Criar um arquivo temporário a partir do blob do PDF
      const file = createFileFromBlob(pdfBlob, fileName);

      // Determinar o link a ser enviado (URL pública ou URL atual)
      const linkToSend = publicUrl || window.location.href;

      // Verificar se é um relatório com base no nome do arquivo ou no título
      const isReport = fileName.toLowerCase().includes('relatorio') ||
        (proposalName && proposalName.toLowerCase().includes('relatório'));

      // Determinar o tipo de relatório (inspeção ou projeto)
      let reportType = "inspection";
      if (fileName.toLowerCase().includes('projeto') ||
        (proposalName && proposalName.toLowerCase().includes('projeto'))) {
        reportType = "project";
      }

      // Criar um FormData para enviar o arquivo
      const formData = new FormData();
      formData.append('type', isReport ? 'report' : 'proposal');
      formData.append('to', customerEmail);
      formData.append('proposalName', proposalName || `Relatório: ${fileName}`);
      formData.append('proposalLink', linkToSend);
      formData.append('customerName', customerName || '');
      formData.append('file', file);
      formData.append('isContract', 'false');

      // Adicionar o tipo de relatório se for um relatório
      if (isReport) {
        formData.append('reportType', reportType);
      }

      // Enviar o email com o anexo
      const response = await fetch('/api/email-with-attachment', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast({
          title: "Sucesso",
          description: "Relatório enviado com sucesso para o cliente.",
        });
      } else {
        toast({
          title: "Erro",
          description: result.error || "Falha ao enviar o relatório. Tente novamente.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Erro ao enviar relatório por email:", error);
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao enviar o relatório por email.",
        variant: "destructive",
      });
    } finally {
      setSendingEmail(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0 gap-0">
        <DialogHeader className="p-4 border-b">
          <div className="flex justify-between items-start">
            <div>
              <DialogTitle>Visualização do Relatório</DialogTitle>
              <DialogDescription>
                Visualize o relatório antes de salvá-lo
              </DialogDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8 p-0 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex justify-end items-center mt-4">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                Salvar PDF
              </Button>

              {showEmailButton && customerEmail && pdfBlob && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSendEmail}
                  disabled={sendingEmail}
                  className="flex items-center gap-1 bg-green-500 hover:bg-green-600 text-white hover:text-white border-green-500 hover:border-green-600"
                >
                  {sendingEmail ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Enviando...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Enviar por e-mail
                    </>
                  )}
                </Button>
              )}

              {publicUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(publicUrl);
                    toast({
                      title: "Link copiado",
                      description: "Link público copiado para a área de transferência",
                    });
                  }}
                  className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white hover:text-white border-blue-500 hover:border-blue-600"
                >
                  <Link className="h-4 w-4" />
                  Copiar link público
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-auto relative">
          {loading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <div className="flex flex-col items-center gap-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="text-sm text-muted-foreground">
                  Carregando PDF...
                </p>
              </div>
            </div>
          )}
          <iframe
            src={pdfUrl}
            className="w-full h-full"
            onLoad={() => setLoading(false)}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
