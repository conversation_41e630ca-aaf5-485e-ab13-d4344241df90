import {
  Bolt,
  BriefcaseBusiness,
  ChartSpline,
  CircleDollarSign,
  CloudSunRain,
  FileChartColumn,
  FileChartPie,
  FileText,
  FileType2,
  HardHat,
  Home,
  Kanban,
  ScanSearch,
  SearchCheckIcon,
  Settings,
  TrendingUp,
  UserPlus,
  Users,
  Wrench,
  FileX2,
  FileCheck2,
  FileClock,
  FileBox,
  SquareStack,
  UserPen,
  FileScan,
  FolderSearch,
} from "lucide-react";
import { RouteDataInterface } from "../lib/routes/routes";
import { ProposalSituation } from "../types/core/proposal";

export const documentMask = {
  CPF: { mask: "000.000.000-00", maxLength: 14 },
  CNPJ: { mask: "00.000.000/0000-00", maxLength: 18 },
  RG: { mask: "00.000.000-0", maxLength: 12 },
  CNH: { mask: "***********", maxLength: 11 },
};

export const proposalSituations: { value: ProposalSituation; label: string }[] =
  [
    { value: "NEW", label: "Nova proposta" },
    { value: "UNDER_ANALYSIS", label: "Em análise" },
    { value: "PROPOSAL_SENT", label: "Proposta enviada" },
    { value: "PROPOSAL_ACCEPTED", label: "Proposta aceita" },
    { value: "SIGN_REQUESTED", label: "Solicitação de assinatura" },
    { value: "SIGNED", label: "Assinado" },
    { value: "PROJECT_IN_PROGRESS", label: "Projeto em andamento" },
    { value: "PROJECT_FINISHED", label: "Projeto concluído" },
    { value: "LOST", label: "Perdido" },
  ];

export const proposalTemplatePlaceholder = `
<div style="font-weight: bold;">Atenção, os campos abaixo serão preenchidos automaticamente com os dados da proposta vinculada ao template</div>
<br/>
[titulo]
<br/>
[cliente]
<br/>
[data_inicio]
<br/>
[data_fim]
<br/>
[orcamento]
<br/>
[condicao_pagamento]
<br/>
[escopo_servicos]
<br/>
[planejamentos]
<br/>
[metodologias]
`;

export const proposalTokens = {
  name: "[titulo]",
  customer: "[cliente]",
  startDate: "[data_inicio]",
  endDate: "[data_fim]",
  budget: "[orcamento]",
  paymentCondition: "[condicao_pagamento]",
  serviceScopes: "[escopo_servicos]",
  plannings: "[planejamentos]",
  methodology: "[metodologias]",
};

export const reportTemplatePlaceholder = `
<div style="font-weight: bold;">Atenção, os campos abaixo serão preenchidos automaticamente com os dados do relatório vinculada ao template</div>
<br/>
[titulo]
<br/>
[cliente]
<br/>
[data_inicio]
<br/>
[data_fim]
<br/>
[orcamento]
<br/>
[condicao_pagamento]
<br/>
[escopo_servicos]
<br/>
[metodologias]
`;

export const reportTokens = {
  name: "[titulo]",
  customer: "[cliente]",
  startDate: "[data_inicio]",
  endDate: "[data_fim]",
  budget: "[orcamento]",
  paymentCondition: "[condicao_pagamento]",
  serviceScopes: "[escopo_servicos]",
  methodology: "[metodologias]",
};

export const breadcrumbs: RouteDataInterface[] = [
  {
    title: "Painel de controle",
    url: "/views/control-panel",
    icon: Home,
  },
  {
    title: "CRM",
    url: "",
    icon: BriefcaseBusiness,
    routerGroup: true,
    items: [
      {
        title: "Clientes",
        url: "/views/crm/customers",
        icon: Users,
        items: [
          {
            title: "Editar Cliente",
            url: "/views/crm/customers/[id]",
            icon: UserPen,
          },
          {
            title: "Novo Cliente",
            url: "/views/crm/customers/add",
            icon: UserPlus,
          },
        ],
      },
      {
        title: "Propostas",
        url: "/views/crm/proposals",
        icon: FileText,
      },
      {
        title: "Gerenciamento de Propostas",
        url: "/views/crm/proposals/management",
        icon: Kanban,
      },
      {
        title: "Contratos a iniciar",
        url: "/views/crm/proposals/to-start",
        icon: FileText,
        items: [
          {
            title: "Histórico",
            url: "/views/crm/proposals/completed/[id]?from=to-start",
            icon: SquareStack,
          },
        ],
      },
      {
        title: "Contratos em andamento",
        url: "/views/crm/proposals/accepted",
        icon: FileClock,
        items: [
          {
            title: "Parâmetros de Inspeção",
            url: "/views/crm/proposals/inspection-parameters",
            disableLink: true,
            icon: ScanSearch,
            items: [
              {
                title: "Novo parâmetro",
                url: "/views/crm/proposals/inspection-parameters/form",
                icon: SearchCheckIcon,
              },
            ],
          },
          {
            title: "Acompanhamento de Obra",
            url: "/views/crm/proposals/services-budget",
            icon: CircleDollarSign,
          },
          {
            title: "Histograma",
            url: "/views/crm/proposals/histogram",
            icon: ChartSpline,
          },
          {
            title: "Serviços para medição",
            url: "/views/crm/proposals/histogram/services",
            icon: Wrench,
          },
          {
            title: "Histórico",
            url: "/views/crm/proposals/completed/[id]?from=accepted",
            icon: SquareStack,
          },
        ],
      },
      {
        title: "Contratos Concluídos",
        url: "/views/crm/proposals/completed",
        icon: FileCheck2,
        items: [
          {
            title: "Histórico",
            url: "/views/crm/proposals/completed/[id]?from=completed",
            icon: SquareStack,
          },
        ],
      },
      {
        title: "Contratos perdidos",
        url: "/views/crm/proposals/lost",
        icon: FileX2,
        items: [
          {
            title: "Histórico",
            url: "/views/crm/proposals/completed/[id]?from=lost",
            icon: SquareStack,
          },
        ],
      },
      {
        title: "KPI",
        url: "/views/crm/kpi",
        icon: TrendingUp,
      },
    ],
  },
  {
    title: "Laudos e Relatórios",
    url: "",
    icon: FileChartPie,
    routerGroup: true,
    items: [
      {
        title: "Laudo de Inspeção",
        url: "/views/inspection-report",
        icon: FileChartColumn,
      },
      {
        title: "Relatório de Projeto",
        url: "/views/project-report",
        icon: FileScan,
      },
      {
        title: "Relatório de Fiscalização",
        url: "/views/construction-inspection",
        icon: FileBox,
        items: [
          {
            title: "Relatório de Impacto Climático",
            url: "/views/construction-inspection/weather-impact/[id]",
            icon: CloudSunRain,
          },
          {
            title: "Fiscalizações",
            url: "/views/construction-inspection/[id]",
            icon: FileBox,
          },
        ],
      },
      {
        title: "Consultoria",
        url: "/views/consultancy-report",
        icon: FolderSearch,
      },
    ],
  },
  {
    title: "Configuração",
    url: "",
    icon: Settings,
    routerGroup: true,
    items: [
      {
        title: "Escopo de serviços",
        url: "/views/services-scopes",
        icon: Bolt,
      },
      {
        title: "Templates do Sistema",
        url: "", // Sem rota, apenas para expandir
        icon: FileType2,
        items: [
          {
            title: "Proposta",
            url: "/views/proposal-templates?type=PROPOSAL",
            icon: FileText,
          },
          {
            title: "Contrato",
            url: "/views/proposal-templates?type=CONTRACT",
            icon: FileBox,
          },
          {
            title: "Fiscalização e Gerenciamento",
            url: "/views/proposal-templates?type=SUPERVISION",
            icon: FileChartPie,
          },
          {
            title: "Inspeção e Consultoria",
            url: "/views/proposal-templates?type=INSPECTION",
            icon: FileChartColumn,
          },
          {
            title: "Projetos",
            url: "/views/proposal-templates?type=PROJECT",
            icon: FileScan,
          },
          {
            title: "Consultoria",
            url: "/views/proposal-templates?type=CONSULTANCY",
            icon: FolderSearch,
          },
          // {
          //   title: "Outros Templates",
          //   url: "/views/report-templates",
          //   icon: Files,
          // },
        ],
      },

      {
        title: "Equipamentos e mão de obra",
        url: "/views/labor-equipaments",
        icon: HardHat,
      },
      {
        title: "Gerenciar membros",
        url: "/views/manage-members",
        icon: Users,
      },
    ],
  },
];

export const states = [
  {
    value: "AC",
    label: "AC",
  },
  {
    value: "AL",
    label: "AL",
  },
  {
    value: "AP",
    label: "AP",
  },
  {
    value: "AM",
    label: "AM",
  },
  {
    value: "BA",
    label: "BA",
  },
  {
    value: "CE",
    label: "CE",
  },
  {
    value: "DF",
    label: "DF",
  },
  {
    value: "ES",
    label: "ES",
  },
  {
    value: "GO",
    label: "GO",
  },
  {
    value: "MA",
    label: "MA",
  },
  {
    value: "MT",
    label: "MT",
  },
  {
    value: "MS",
    label: "MS",
  },
  {
    value: "MG",
    label: "MG",
  },
  {
    value: "PA",
    label: "PA",
  },
  {
    value: "PB",
    label: "PB",
  },
  {
    value: "PR",
    label: "PR",
  },
  {
    value: "PE",
    label: "PE",
  },
  {
    value: "PI",
    label: "PI",
  },
  {
    value: "RJ",
    label: "RJ",
  },
  {
    value: "RN",
    label: "RN",
  },
  {
    value: "RS",
    label: "RS",
  },
  {
    value: "RO",
    label: "RO",
  },
  {
    value: "RR",
    label: "RR",
  },
  {
    value: "SC",
    label: "SC",
  },
  {
    value: "SP",
    label: "SP",
  },
  {
    value: "SE",
    label: "SE",
  },
  {
    value: "TO",
    label: "TO",
  },
];

export const reportTypes = [
  {
    value: "REPORT",
    label: "Laudo",
  },
  {
    value: "KPI",
    label: "KPI",
  },
  {
    value: "HISTOGRAM",
    label: "Histograma",
  },
  {
    value: "CLIMATE_IMPACT",
    label: "Impacto climatico",
  },
  {
    value: "PROJECT",
    label: "Projeto",
  },
];
