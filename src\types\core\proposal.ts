import { PaymentCondition, Periodicity } from "../common";
import { ResourceControlInterface } from "../utils";
import { Contract, ContractInterface } from "./contract";
import { CustomerRelationInterface } from "./customer";
import { FileRelationInterface } from "./file";
import { InspectionParameter } from "./inspection-paramenters";
import { ProposalTemplateRelationInterface } from "./proposal-template";
import { RepairBudget } from "./repair-budget";
import { ServicesScope } from "./services-scope";

export type ProposalSituation =
  | "NEW"
  | "UNDER_ANALYSIS"
  | "PROPOSAL_SENT"
  | "PROPOSAL_ACCEPTED"
  | "SIGN_REQUESTED"
  | "SIGNED"
  | "PROJECT_IN_PROGRESS"
  | "PROJECT_FINISHED"
  | "LOST";

export interface ProposalFilters {
  situation?: ProposalSituation | ProposalSituation[];
  customerId?: string;
  startDate?: string | Date;
  page?: number;
  pageSize?: number;
  search?: string;
  serviceType?: string | string[];
}

export interface ProposalInterface {
  name: string;
  startDate: string | Date;
  endDate: string | Date;
  area: number;
  budget: number;
  workTotalCost?: number; // Adicionado campo workTotalCost
  paymentCondition: PaymentCondition;
  downPayment?: number;
  installmentNumber?: number;
  installmentAmount?: number;
  situation: ProposalSituation;
  periodicity: Periodicity;
  customService?: string;
  order: number;
  cep?: string;
  address?: string;
  city?: string;
  state?: string;
  serviceType?: string;
  serviceScopes: ServicesScope[];
  isCustomProject?: boolean; // Indica se é um projeto personalizado
  plannings: Planning[];
  repairBudgets: RepairBudget[];
  inspectionParameters: InspectionParameter[];
  contract: Contract;
}

export interface ProposalRelationInterface {
  proposalId: string;
  proposal: ProposalInterface;
}

export type Proposal = ProposalInterface &
  Partial<ProposalTemplateRelationInterface> &
  ResourceControlInterface &
  Partial<CustomerRelationInterface> &
  Partial<FileRelationInterface> &
  Partial<ContractInterface> & {
    // Suporte a múltiplos arquivos (usado principalmente em projetos)
    files?: Array<{
      id: string;
      name: string;
      type: string;
      path: string;
      size: number;
      uploadedAt: string | Date;
    }>;
  };

export interface PlanningFrequencyItemInterface {
  order: number;
  content: string;
  label: string;
}

export type Planning = PlanningFrequencyItemInterface &
  Partial<ProposalRelationInterface> &
  ResourceControlInterface;
