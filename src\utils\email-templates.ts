// URL da logo da Ageu (usando URL absoluta para garantir que funcione em emails)
export const AGEU_LOGO_URL =
  "https://ageu.eng.br/_next/static/media/logo.361514b7.svg";

// Template de email para proposta ou contrato com logomarca
export const getProposalEmailTemplate = (
  customerName: string,
  proposalName: string,
  proposalLink: string,
  isContract: boolean = false
) => {
  const documentType = isContract ? "Contrato" : "Proposta Comercial";
  const viewButtonText = isContract
    ? "Visualizar Contrato"
    : "Visualizar Proposta";
  const messageText = isContract
    ? `Temos o prazer de enviar o contrato <strong>${proposalName}</strong> para sua assinatura.`
    : `Temos o prazer de enviar a proposta comercial <strong>${proposalName}</strong> para sua análise.`;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <img src="${AGEU_LOGO_URL}" alt="Ageu Engenharia" style="max-width: 150px; height: auto;" />
        <h1 style="color: #333; margin-top: 20px;">${documentType}</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Prezado(a) <strong>${customerName}</strong>,</p>
        <p>${messageText}</p>
        <p>Você encontrará todos os detalhes ${
          isContract ? "do contrato" : "da proposta"
        } no arquivo anexado a este email.</p>
        <p>Para visualizar ${
          isContract ? "o contrato" : "a proposta"
        } online, por favor clique no botão abaixo:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${proposalLink}" style="background-color: #2196F3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">${viewButtonText}</a>
        </div>
        <p>Caso o botão não funcione, copie e cole o link abaixo em seu navegador:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${proposalLink}</p>
        <p>Estamos à disposição para esclarecer quaisquer dúvidas ou fornecer informações adicionais.</p>
        <p>Atenciosamente,</p>
        <p><strong>Administração de Contratos Ageu</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Email para contato: <a href="mailto:<EMAIL>" style="color: #2196F3; text-decoration: none;"><EMAIL></a></p>
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;
};

// Template de email para relatórios de inspeção ou projeto
export const getReportEmailTemplate = (
  customerName: string,
  reportName: string,
  reportLink: string,
  reportType: "inspection" | "project" = "inspection"
) => {
  const documentType =
    reportType === "inspection"
      ? "Relatório de Inspeção"
      : "Relatório de Projeto";
  const viewButtonText = "Visualizar Relatório";
  const messageText = `Temos o prazer de enviar o ${documentType.toLowerCase()} <strong>${reportName}</strong> para sua análise.`;

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <img src="${AGEU_LOGO_URL}" alt="Ageu Engenharia" style="max-width: 150px; height: auto;" />
        <h1 style="color: #333; margin-top: 20px;">${documentType}</h1>
      </div>
      <div style="color: #555; line-height: 1.5;">
        <p>Prezado(a) <strong>${customerName}</strong>,</p>
        <p>${messageText}</p>
        <p>Você encontrará todos os detalhes do relatório no arquivo anexado a este email.</p>
        <p>Para visualizar o relatório online, por favor clique no botão abaixo:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${reportLink}" style="background-color: #2196F3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">${viewButtonText}</a>
        </div>
        <p>Caso o botão não funcione, copie e cole o link abaixo em seu navegador:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">${reportLink}</p>
        <p>Estamos à disposição para esclarecer quaisquer dúvidas ou fornecer informações adicionais.</p>
        <p>Atenciosamente,</p>
        <p><strong>Administração Ageu</strong></p>
      </div>
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>Email para contato: <a href="mailto:<EMAIL>" style="color: #2196F3; text-decoration: none;"><EMAIL></a></p>
        <p>Este é um email automático, por favor não responda.</p>
        <p>© ${new Date().getFullYear()} Ageu Engenharia. Todos os direitos reservados.</p>
      </div>
    </div>
  `;
};
